<?php

/**
 * Script simple pour appliquer les index de performance
 * Usage: php apply_performance_indexes.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load(__DIR__ . '/.env');

// Configuration de la base de données
$host = $_ENV['DATABASE_HOST'] ?? 'localhost';
$port = $_ENV['DATABASE_PORT'] ?? '3306';
$dbname = $_ENV['DATABASE_NAME'] ?? 'newbe';
$username = $_ENV['DATABASE_USER'] ?? 'root';
$password = $_ENV['DATABASE_PASSWORD'] ?? '';

try {
    // Connexion à la base de données
    $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "✅ Connexion à la base de données réussie\n";
    echo "📊 Base de données: {$dbname}\n\n";

    // Analyser les performances actuelles
    echo "📈 Analyse des performances actuelles:\n";
    echo "=====================================\n";

    $totalDocuments = $pdo->query("SELECT COUNT(*) FROM document")->fetchColumn();
    echo "📄 Total documents: " . number_format($totalDocuments) . "\n";

    $documentsWithSteps = $pdo->query(
        "SELECT COUNT(*) FROM document WHERE current_steps IS NOT NULL AND current_steps != '{}'"
    )->fetchColumn();
    echo "🔄 Documents avec étapes: " . number_format($documentsWithSteps) . "\n";

    $documentsWithTimestamps = $pdo->query(
        "SELECT COUNT(*) FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''"
    )->fetchColumn();
    echo "⏰ Documents avec timestamps: " . number_format($documentsWithTimestamps) . "\n";

    $totalVisas = $pdo->query("SELECT COUNT(*) FROM visa")->fetchColumn();
    echo "✅ Total visas: " . number_format($totalVisas) . "\n\n";

    // Index de performance à créer
    $indexQueries = [
        // Index pour current_steps JSON - les plus importants
        "CREATE INDEX IF NOT EXISTS idx_current_steps_costing ON document ((JSON_EXTRACT(current_steps, '$.Costing')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_quality ON document ((JSON_EXTRACT(current_steps, '$.Quality')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_assembly ON document ((JSON_EXTRACT(current_steps, '$.Assembly')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_machining ON document ((JSON_EXTRACT(current_steps, '$.Machining')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_planning ON document ((JSON_EXTRACT(current_steps, '$.Planning')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_be_0 ON document ((JSON_EXTRACT(current_steps, '$.BE_0')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_be_1 ON document ((JSON_EXTRACT(current_steps, '$.BE_1')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_be ON document ((JSON_EXTRACT(current_steps, '$.BE')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_produit ON document ((JSON_EXTRACT(current_steps, '$.Produit')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_qual_logistique ON document ((JSON_EXTRACT(current_steps, '$.Qual_Logistique')))",
        "CREATE INDEX IF NOT EXISTS idx_current_steps_logistique ON document ((JSON_EXTRACT(current_steps, '$.Logistique')))",
        
        // Index pour state_timestamps
        "CREATE INDEX IF NOT EXISTS idx_state_timestamps_not_null ON document (state_timestamps) WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''",
        
        // Index composites pour les requêtes fréquentes
        "CREATE INDEX IF NOT EXISTS idx_document_supervisor_timestamps ON document (superviseur_id, state_timestamps) WHERE state_timestamps IS NOT NULL",
        "CREATE INDEX IF NOT EXISTS idx_visa_released_drawing_name_status ON visa (released_drawing_id, name, status)",
        "CREATE INDEX IF NOT EXISTS idx_visa_validator_date ON visa (validator_id, date_visa)",
        
        // Index pour les champs fréquemment utilisés
        "CREATE INDEX IF NOT EXISTS idx_document_reference_rev ON document (reference, ref_rev)",
        "CREATE INDEX IF NOT EXISTS idx_document_material ON document (material)",
        "CREATE INDEX IF NOT EXISTS idx_document_proc_type ON document (proc_type)",
        "CREATE INDEX IF NOT EXISTS idx_document_doc_type ON document (doc_type)",
        "CREATE INDEX IF NOT EXISTS idx_document_pris_dans1 ON document (pris_dans1)",
    ];

    echo "🔧 Création des index de performance:\n";
    echo "====================================\n";

    $successCount = 0;
    $errorCount = 0;
    $totalQueries = count($indexQueries);

    foreach ($indexQueries as $i => $query) {
        $progress = $i + 1;
        $percentage = round(($progress / $totalQueries) * 100);
        
        try {
            $pdo->exec($query);
            $successCount++;
            
            // Extraire le nom de l'index de la requête
            preg_match('/idx_[a-zA-Z0-9_]+/', $query, $matches);
            $indexName = $matches[0] ?? 'unknown';
            
            echo "✅ [{$percentage}%] Index créé: {$indexName}\n";
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ [{$percentage}%] Erreur: " . $e->getMessage() . "\n";
        }
    }

    echo "\n📊 Résumé:\n";
    echo "==========\n";
    echo "✅ Index créés avec succès: {$successCount}\n";
    echo "❌ Erreurs: {$errorCount}\n";
    echo "📈 Total: {$totalQueries}\n\n";

    // Analyser l'impact des nouveaux index
    if ($successCount > 0) {
        echo "📏 Analyse de l'impact des nouveaux index:\n";
        echo "==========================================\n";
        
        try {
            $indexSizes = $pdo->query("
                SELECT 
                    INDEX_NAME,
                    ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'Index Size (MB)'
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'document'
                AND INDEX_NAME LIKE 'idx_%'
                GROUP BY INDEX_NAME
                ORDER BY INDEX_LENGTH DESC
                LIMIT 10
            ")->fetchAll();
            
            if (!empty($indexSizes)) {
                echo "🔝 Top 10 des index par taille:\n";
                foreach ($indexSizes as $index) {
                    echo "   📊 {$index['INDEX_NAME']}: {$index['Index Size (MB)']} MB\n";
                }
            }
        } catch (PDOException $e) {
            echo "⚠️  Impossible d'analyser la taille des index: " . $e->getMessage() . "\n";
        }
    }

    echo "\n🎉 Optimisation des performances terminée!\n";
    echo "\n💡 Recommandations:\n";
    echo "===================\n";
    echo "1. Surveillez les performances des requêtes après l'ajout des index\n";
    echo "2. Utilisez EXPLAIN sur vos requêtes pour vérifier l'utilisation des index\n";
    echo "3. Considérez l'ajout d'index supplémentaires basés sur vos patterns de requêtes\n";
    echo "4. Surveillez l'espace disque utilisé par les index\n";

} catch (PDOException $e) {
    echo "❌ Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
    echo "🔧 Vérifiez vos paramètres de connexion dans le fichier .env\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    exit(1);
}
