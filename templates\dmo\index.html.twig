{% extends 'base.html.twig' %}

{% block title %}Liste des DMO{% endblock %}
{% block navbar %}
{% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}
<div class="mt-3" style="margin: 0 7%;">
    {# Messages flash #}
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endfor %}

    <div class="d-flex justify-content-between align-items-center mb-3">

        <h3 class="mb-0">Liste des DMO</h3>
        <div class="text-center ">
            <a href="{{ path('app_dmo_new') }}" class="btn" style="background-color: #009BFF; color: white;">
                <i class="bi bi-plus-circle"></i> Nouvelle DMO
            </a>
        </div>
    </div>
    <form method="GET" action="{{ path('app_dmo_index') }}" class="mb-3 bg-light p-3 rounded-2 shadow">
        {# Filtres principaux #}
        <div class="row">
            <div class="col-md-2 mb-2">
                <label for="filter-dmo" class="form-label">DMO</label>
                <input id="filter-dmo" type="text" name="dmo" class="form-control" placeholder="DMO200309Y" value="{{ app.request.query.get('dmo') }}">
            </div>
            <div class="col-md-2 mb-2">
                <label for="filter-dateInit" class="form-label">Date Initial</label>
                <input id="filter-dateInit" type="date" name="dateInit" class="form-control" value="{{ app.request.query.get('dateInit') }}">
            </div>
            <div class="col-md-3 mb-2">
                <label for="filter-description" class="form-label">Description</label>
                <input id="filter-description" type="text" name="description" class="form-control" placeholder="Description" value="{{ app.request.query.get('description') }}">
            </div>
            <div class="col-md-2 mb-2">
                <label for="filter-project" class="form-label">Projet</label>
                <select id="project-select" name="project" class="form-control">
                    <option value="">Projet</option>
                    {% for project in projects %}
                        {% if project.otp() == app.request.query.get('project') %}
                            <option value="{{ project.id() }}" selected>{{ project.otp() }}</option>
                        {% else %}
                            <option value="{{ project.id() }}">{{ project.otp() }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 mb-2">
                <label for="filter-requestor" class="form-label">Demandeur</label>
                <select id="filter-requestor" name="requestor" class="form-control">
                    <option value="">Demandeur</option>
                    {% for user in users %}
                        {% if user.username == app.request.query.get('requestor') %}
                            <option value="{{ user.username }}" selected>{{ user.getNom }} {{ user.getPrenom }}</option>
                        {% else %}
                            <option value="{{ user.username }}">{{ user.getNom }} {{ user.getPrenom }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2 mb-2">
                <label for="filter-decision" class="form-label">Décision</label>
                <select id="filter-decision" name="decision" class="form-control">
                    <option value="">Décision</option>
                    <option value="CREATED">Created</option>
                    <option value="accept">Accept</option>
                    <option value="reject">Rejected</option>
                    <option value="under-review">Under Review</option>
                </select>
            </div>
            <div class="col-md-2 mb-2">
                <label for="filter-status" class="form-label">Statut</label>
                <select id="filter-status" name="status" class="form-control">
                    <option value="">Statut</option>
                    <option value="1">OPEN</option>
                    <option value="0">CLOSED</option>
                </select>
            </div>
            <div class="col-md-2 mb-2">
                <label for="filter-ex" class="form-label">Ex</label>
                <select id="filter-ex" name="ex" class="form-control">
                    <option value="">Ex</option>
                    <option value="ATEX">ATEX</option>
                    <option value="CSA">CSA</option>
                    <option value="EX">EX</option>
                    <option value="IECEX">IECEX</option>
                    <option value="NO">NO</option>
                </select>
            </div>
            <div class="col-md-3 mb-2">
                <label for="filter-engOwner" class="form-label">Propriétaire BE</label>
                <select id="filter-engOwner" name="engOwner" class="form-control">
                    <option value="">Propriétaire BE</option>
                    {% for user in users_bde %}
                        {% if user.username == app.request.query.get('engOwner') %}
                            <option value="{{ user.username }}" selected>{{ user.getNom }} {{ user.getPrenom }}</option>
                        {% else %}
                            <option value="{{ user.username }}">{{ user.getNom }} {{ user.getPrenom }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 mb-2 d-flex align-items-end">
                    <button type="button" class="btn btn-secondary me-1 w-100 position-relative" data-bs-toggle="collapse" id="advancedtoogle" data-bs-target="#advancedFilters" aria-expanded="true" aria-controls="advancedFilters">Filtres avancés
                        {% if app.request.query.get('dateEnd') or app.request.query.get('prNumber') or app.request.query.get('lastUpdateDate') or app.request.query.get('lastModificator') or app.request.query.get('type') or app.request.query.get('document') or app.request.query.get('productRange') %}
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            {% set count = 0 %}
                            {% if app.request.query.get('dateEnd') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('prNumber') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('lastUpdateDate') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('lastModificator') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('type') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('document') %}{% set count = count + 1 %}{% endif %}
                            {% if app.request.query.get('productRange') %}{% set count = count + 1 %}{% endif %}
                            {{ count }}
                            <span class="visually-hidden">Nombre de filtres actifs</span>
                        </span>
                        <i class="fas fa-angle-up"></i>
                        {% else %}
                        <i class="fas fa-angle-down"></i>
                        {% endif %}
                    </button>
                <button type="submit" class="btn btn-primary ms-1 w-100">Rechercher</button>
            </div>
        </div>

        {# Filtres avancés masqués par défaut #}
        {% if app.request.query.get('dateEnd') or app.request.query.get('prNumber') or app.request.query.get('lastUpdateDate') or app.request.query.get('lastModificator') or app.request.query.get('type') or app.request.query.get('document') or app.request.query.get('productRange') %}
        <div class="collapse show" id="advancedFilters">
        {% else %}
        <div class="collapse" id="advancedFilters">
        {% endif %}
            <div class="card card-body mt-2">
                <div class="row">
                    <div class="col-md-2 mb-2">
                        <label for="filter-dateEnd" class="form-label">Date Fin</label>
                        <input id="filter-dateEnd" type="date" name="dateEnd" class="form-control" value="{{ app.request.query.get('dateEnd') }}">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="filter-prNumber" class="form-label">N° PR</label>
                        <input id="filter-prNumber" type="text" name="prNumber" class="form-control" placeholder="PR Number" value="{{ app.request.query.get('prNumber') }}">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="filter-lastUpdateDate" class="form-label">Dernière MàJ</label>
                        <input id="filter-lastUpdateDate" type="date" name="lastUpdateDate" class="form-control" value="{{ app.request.query.get('lastUpdateDate') }}">
                    </div>

                    <div class="col-md-2 mb-2">
                        <label for="filter-lastModificator" class="form-label">Dernier Modificateur</label>
                        <select id="filter-lastModificator" name="lastModificator" class="form-select">
                            <option value="">Dernier Modificateur</option>
                            {% for user in users %}
                                {% if user.username == app.request.query.get('lastModificator') %}
                                    <option value="{{ user.username }}" selected>{{ user.getNom }} {{ user.getPrenom }}</option>
                                {% else %}
                                    <option value="{{ user.username }}">{{ user.getNom }} {{ user.getPrenom }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="filter-type" class="form-label">Type</label>
                        <select id="filter-type" name="type" class="form-control">
                            <option value="">Type</option>
                            {% set options = [
                                'Method Lab.', 'Method Assy.', 'Engineering'
                            ] %}
                            {% for option in options %}
                                {% if option == app.request.query.get('type') %}
                                    <option value="{{ option }}" selected>{{ option }}</option>
                                {% else %}
                                    <option value="{{ option }}">{{ option }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="filter-document" class="form-label">Document</label>
                        <select id="filter-document" name="document" class="form-control">
                            <option value="">Select Document</option>
                            {% set options = [
                                'DEO', 'Drawing', 'NT', 'NU', 'Specification', 'Other', 'Assembly Tool', 'Assy. Checklist',
                                'Bonne Prat.', 'COSIR', 'FI', 'FUM', 'IRS', 'OSIR', 'PHI', 'SAP Drawing', 'FATP', 'FOL',
                                'Lab. Tool', 'QPP', 'QPR', 'Security Chklst'
                            ] %}
                            {% for option in options %}
                                {% if option == app.request.query.get('document') %}
                                    <option value="{{ option }}" selected>{{ option }}</option>
                                {% else %}
                                    <option value="{{ option }}">{{ option }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label for="filter-productRange" class="form-label">Gamme Produit</label>
                        <select id="productRange" name="productRange" class="form-select">
                            <option value="">-- Sélectionner une division --</option>
                        </select>
                    </div>

                </div>
            </div>
        </div>
    </form>
    {# Pagination en haut #}
    <div class="d-flex justify-content-center mb-3">
        {{ knp_pagination_render(dmos) }}
    </div>

    {% if dmos.items|length > 0 %}

        {% for dmo in dmos.items %}
            <div class="card mb-3 shadow border-0">
                <div class="card-header text-black bg-light" style="background: linear-gradient(0deg,rgba(255, 255, 255, 0) 0%,rgba(255, 255, 255, 0.64) 44%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ dmo.getDmoId }}</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end p-2 m-2 rounded-4 shadow" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item rounded-2 ps-3 pe-3 pt-2 pb-2" href="{{ path('app_dmo_show', {'id': dmo.id}) }}"><i class="me-2 fa fa-pencil text-primary"></i> Modifier</a></li>
                                <li class="dropdown-item rounded-2 ps-3 pe-3 pt-2 pb-2">{% include 'dmo/_delete_form.html.twig' %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Dates -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Date Init:</strong> {{ dmo.dateInit ? dmo.dateInit|date('Y-m-d H:i:s') : '' }}
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>Date End:</strong> {{ dmo.dateEnd ? dmo.dateEnd|date('Y-m-d H:i:s') : '' }}
                        </div>
                    </div>
                    <!-- Description -->
                    <div class="mb-3">
                        <strong>Description:</strong>
                        <p>{{ dmo.Description|nl2br }}</p>
                    </div>
                    <hr>
                    <!-- Range & Project -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Division:</strong> {{ dmo.getNameDivisonProductRange }}
                        </div>
                        <div class="col-md-4">
                            <strong>Range:</strong> {{ dmo.getNameProductRange }}
                        </div>
                        <div class="col-md-4">
                            <strong>Project:</strong> {{ dmo.getProjectRelation ? dmo.getProjectRelation.otp() : '' }}
                        </div>
                    </div>
                    <!-- Type / Doc / Division -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Type:</strong> {{ dmo.type }}
                        </div>
                        <div class="col-md-4">
                            <strong>Doc:</strong> {{ dmo.document }}
                        </div>

                    </div>
                    <hr>
                    <!-- Requestor & Departement -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Requestor:</strong> {{ dmo.getRequestor.nom()~ ' ' ~ dmo.getRequestor.prenom() }}
                        </div>
                        <div class="col-md-4">
                            <strong>Departement:</strong> {{ dmo.getDepartementRequestor }}
                        </div>
                    </div>
                    <!-- Decision, Status, Eng Owner -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Decision:</strong> {{ dmo.Decision }}
                        </div>
                        <div class="col-md-4">
                            <strong>Status:</strong> {{ dmo.status ? 'OPEN' : 'CLOSED' }}
                        </div>
                        <div class="col-md-4">
                            <strong>Eng Owner:</strong> {{ dmo.getNameEngOwner }} {{ dmo.getPrenomEngOwner }}
                        </div>
                    </div>
                    <hr>
                    <!-- PR Number -->
                    <div class="mb-2">
                        <strong>Pr Number:</strong> {{ dmo.PrNumber }}
                    </div>
                </div>
            </div>
        {% endfor %}

        {# Pagination en bas de page #}
        <div class="d-flex justify-content-center mt-4">
            {{ knp_pagination_render(dmos) }}
        </div>
    {% else %}
        <div class="alert alert-info text-center">
            Aucun DMO trouvé
        </div>
    {% endif %}

</div>

<script>
    $(document).ready(function() {
        /*$.ajax({
            url: "https://frscmoutils.scmlemans.com/TimeSheet/TS_REQUEST_SROCHDI.php",
            method: "GET",
            xhrFields: {
                withCredentials: true
            },
            success: function(data) {
                // Supposons que data est un tableau d'objets { id, name }
                data.forEach(function(item) {
                    $("#project-select").append(`<option value="${item}">${item}</option>`);
                });
            },
            error: function() {
                console.error("Erreur lors du chargement des projets.");
            }
        });*/

        $.ajax({
                url: "{{ path('app_dmo_getProductRange') }}", // Endpoint qui renvoie { division1: [range1, range2], division2: [...], ... }
                method: 'GET',
                dataType: 'json'
            }).done(function(data) {
                var select = $('#productRange');
                select.empty();
                select.append('<option value="">-- Sélectionner une division --</option>');
                var selected = "{{ app.request.query.get('productRange') }}";
                $.each(data, function(division, ranges) {
                    var optgroup = $('<optgroup label="'+division+'"></optgroup>');
                    $.each(ranges, function(index, range) {
                        if (range == selected) {
                            optgroup.append('<option value="'+range+'" selected>'+range+'</option>');
                        } else {
                            optgroup.append('<option value="'+range+'">'+range+'</option>');
                        }
                    });
                    select.append(optgroup);
            });
        })


        $('#advancedtoogle').click(function() {
            var icon = $(this).find('i');
            if (icon.hasClass('fa-angle-up')) {
                icon.removeClass('fa-angle-up').addClass('fa-angle-down');
            } else {
                icon.removeClass('fa-angle-down').addClass('fa-angle-up');
            }
        });
    });
</script>
<style>
    .card {
        border-radius: 12px;
    }

    .card-header {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    body {
        background-image: url("{{ asset('images/wave.svg') }}");
        background-repeat: no-repeat;
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.2rem;
    }

    /* Styles pour la pagination */
    .pagination {
        margin-bottom: 0;
    }

    .pagination .page-link {
        color: #009BFF;
        border-color: #dee2e6;
        transition: all 0.2s ease;
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
        border-color: #009BFF;
        box-shadow: 0 2px 8px rgba(0, 155, 255, 0.3);
    }

    .pagination .page-link:hover {
        color: #0056b3;
        background: linear-gradient(135deg, rgba(0, 155, 255, 0.1) 0%, rgba(0, 86, 179, 0.1) 100%);
        border-color: #009BFF;
        transform: translateY(-1px);
    }
</style>
{% endblock %}
