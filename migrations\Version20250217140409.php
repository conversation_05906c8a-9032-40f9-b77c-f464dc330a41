<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250217140409 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE product_range (id INT AUTO_INCREMENT NOT NULL, product_range VARCHAR(255) NOT NULL, division VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE dmo ADD product_range_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CDE132BA26 FOREIGN KEY (product_range_id) REFERENCES product_range (id)');
        $this->addSql('CREATE INDEX IDX_BDC1D3CDE132BA26 ON dmo (product_range_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CDE132BA26');
        $this->addSql('DROP TABLE product_range');
        $this->addSql('DROP INDEX IDX_BDC1D3CDE132BA26 ON dmo');
        $this->addSql('ALTER TABLE dmo DROP product_range_id');
    }
}
