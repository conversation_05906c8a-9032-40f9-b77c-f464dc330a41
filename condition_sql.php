
<?php 

$BE_1_condition='
		   TRIM(Creation_VISA) like "" 
			AND TRIM(VISA_BE_2) like ""
			AND TRIM(VISA_BE_3) like ""
			';
			
$BE_2_condition='
	   TRIM(Creation_VISA) not like "" 
		AND TRIM(VISA_BE_2) like ""
		AND TRIM(VISA_BE_3) like ""
		';

$BE_3_condition='
	   TRIM(Creation_VISA) not like "" 
		AND TRIM(VISA_BE_2) not like ""
		AND TRIM(VISA_BE_3) like ""
		';


$Project_Conditions='
TRIM(tbl_released_drawing.VISA_Project) like ""
AND 
(
	tbl_released_package.Project not like "STAND"
AND TRIM(tbl_released_package.VISA_BE_3) not like ""
AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%")
)';

$Quality_Conditions='
(
	tbl_released_drawing.Doc_Type like "PUR"
 OR tbl_released_drawing.Doc_Type like "ASSY"
 OR tbl_released_drawing.Doc_Type like "DOC"
)
AND TRIM(tbl_released_package.VISA_BE_3) not like ""
AND TRIM(tbl_released_drawing.VISA_Quality) like ""
';

$Product_Conditions='
(
	TRIM(tbl_released_drawing.VISA_Product) like ""
AND TRIM(tbl_released_package.VISA_BE_3) not like ""
)';

$Inventory_Conditions='
(
	TRIM(tbl_released_drawing.VISA_Inventory) like ""
AND TRIM(tbl_released_package.VISA_BE_3) not like ""
AND TRIM(tbl_released_drawing.VISA_GID) like ""
AND tbl_released_drawing.Doc_Type not like "DOC"
AND tbl_released_drawing.Inventory_Impact not like "NO IMPACT"
)';

// ASSEMBLAGE
$Prod_ASSY_Conditions='
	TRIM(tbl_released_drawing.VISA_Product) not like "" 
AND (
	  (tbl_released_drawing.Doc_Type like "ASSY" AND (left(tbl_released_drawing.Proc_Type,1) not like "F")) 
		OR 
	  (tbl_released_drawing.Doc_Type like "DOC")
	)
AND TRIM(tbl_released_drawing.VISA_Prod) like ""
';

$Prod_MACH_Conditions='
	TRIM(tbl_released_drawing.VISA_Product) not like ""
AND tbl_released_drawing.Doc_Type like "MACH"
AND TRIM(tbl_released_drawing.VISA_Prod) like ""
';

$Prod_MOLD_Conditions='
	TRIM(tbl_released_drawing.VISA_Product) not like "" 
AND tbl_released_drawing.Doc_Type like "MOLD"
AND TRIM(tbl_released_drawing.VISA_Prod) like ""
';

$Method_Conditions='
	TRIM(tbl_released_drawing.VISA_Method) like "" 
AND (
		tbl_released_drawing.Doc_Type like "ASSY" 
	 OR tbl_released_drawing.Doc_Type like "DOC" 
	 OR tbl_released_drawing.Material_Type like "PACKAGING"
	 )
AND TRIM(tbl_released_drawing.VISA_Prod) not like ""

';

$Supply_Conditions='
	(
		tbl_released_drawing.Doc_Type like "ASSY"
	 OR tbl_released_drawing.Doc_Type like "MACH"
	 OR tbl_released_drawing.Doc_Type like "MOLD"
	 OR tbl_released_drawing.Doc_Type like "DOC"
	)
AND tbl_released_drawing.VISA_Prod not like ""
AND tbl_released_drawing.VISA_Supply like ""
';

// RFQ FOURNISSEUR
$PUR_1_RFQ_Conditions='
	TRIM(tbl_released_drawing.VISA_PUR_1) like "" 
AND tbl_released_drawing.Doc_Type like "PUR"
AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
AND TRIM(tbl_released_drawing.VISA_Quality) not like ""
AND TRIM(tbl_released_drawing.VISA_Product) not like ""
';

// PRIS DANS FOURNISSEUR
$PUR_2_PRISDANS_Conditions='
	TRIM(tbl_released_drawing.VISA_PUR_2) like ""
AND TRIM(tbl_released_drawing.VISA_PUR_1) not like ""
AND tbl_released_drawing.Proc_Type like "F30"
';

// FIA
$PUR_3_Conditions='
	tbl_released_drawing.VISA_PUR_1 not like "" 
AND TRIM(tbl_released_drawing.VISA_PUR_3) like ""
AND (tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
AND TRIM(tbl_released_drawing.VISA_GID_2) not like ""
';

// ROHS & REACH FOURNISSEUR
$PUR_4_Conditions='
	TRIM(tbl_released_drawing.VISA_PUR_4) like "" 
AND TRIM(tbl_released_drawing.VISA_PUR_1) not like ""
';

// ORIGINE & HTS FOURNISSEUR
$PUR_5_Conditions='
	TRIM(tbl_released_drawing.VISA_PUR_5) like "" 
AND TRIM(tbl_released_drawing.VISA_PUR_3) not like ""
';

$METRO_Conditions='
	(
		(TRIM(tbl_released_drawing.VISA_PROD) not like "" AND tbl_released_drawing.Proc_Type like "E")
		OR
		(TRIM(tbl_released_drawing.VISA_Quality) not like "" AND tbl_released_drawing.Doc_Type like "PUR")
	)
AND TRIM(tbl_released_drawing.VISA_METRO) like ""
';

$Q_PROD_Conditions = '
TRIM(tbl_released_drawing.VISA_METRO) not like "" AND
 (tbl_released_drawing.Doc_Type like "MACH" OR tbl_released_drawing.Doc_Type like "MOLD")
AND TRIM(tbl_released_drawing.VISA_Q_PROD) like ""
';

$LABO_Conditions='
	tbl_released_drawing.Doc_type like "ASSY" 
AND TRIM(tbl_released_drawing.VISA_MOF) not like ""
AND TRIM(tbl_released_drawing.VISA_LABO) like ""
';

$MOF_Conditions='
	(
		(
			tbl_released_drawing.Doc_type like "ASSY" 
		AND TRIM(tbl_released_drawing.VISA_METRO) not like ""
		)
	OR 
		(
			tbl_released_drawing.Doc_type like "DOC" 
		)
	)
AND TRIM(tbl_released_drawing.VISA_PROD) not like ""
AND TRIM(tbl_released_drawing.VISA_MOF) like ""
';


$Finance_Conditions='
	tbl_released_drawing.Doc_type not like "DOC"
AND TRIM(tbl_released_drawing.VISA_Finance) like ""
AND (
		(TRIM(tbl_released_drawing.VISA_PUR_3) not like ""  AND tbl_released_drawing.Doc_type like "PUR")
	OR
		(TRIM(tbl_released_drawing.VISA_ROUTING_ENTRY) not like ""  AND tbl_released_drawing.Doc_type not like "PUR")
	 )
';


$GID_1_Conditions='
(
	(
		(
			TRIM(tbl_released_drawing.VISA_PUR_1) not like ""
		AND tbl_released_drawing.Proc_Type like "F"
		AND tbl_released_drawing.Doc_Type like "PUR"  
		) 
	OR  (
			TRIM(tbl_released_drawing.VISA_PUR_2) not like ""
		AND tbl_released_drawing.Proc_Type like "F30"
		AND tbl_released_drawing.Doc_Type like "PUR"  
		)
	OR  (
			(tbl_released_drawing.Doc_Type like "MACH" OR tbl_released_drawing.Doc_Type like "MOLD") 
		AND TRIM(tbl_released_drawing.VISA_Metro) not like ""
		AND TRIM(tbl_released_drawing.VISA_Supply) not like ""
		)
	OR  (	
			tbl_released_drawing.Doc_Type like "ASSY"
	    AND TRIM(tbl_released_drawing.VISA_Quality) not like "" 
		AND TRIM(tbl_released_drawing.VISA_Metro) not like ""
		AND TRIM(tbl_released_drawing.VISA_Supply) not like ""
		AND (
			(tbl_released_package.Project not like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) not like "" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%"))  
			OR 
			(tbl_released_package.Project not like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) like "" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) 
			OR 
			(tbl_released_package.Project like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) like "")
			)
		)
	OR  (	
			tbl_released_drawing.Doc_Type like "DOC"
	    AND TRIM(tbl_released_drawing.VISA_Quality) not like "" 
		AND TRIM(tbl_released_drawing.VISA_Metro) not like ""
		AND (
			(tbl_released_package.Project not like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) not like "" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%"))  
			OR 
			(tbl_released_package.Project not like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) like "" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) 
			OR 
			(tbl_released_package.Project like "STAND" AND TRIM(tbl_released_drawing.VISA_Project) like "")
			)
		)
		
	)

	AND 
	
	(
		(TRIM(tbl_released_drawing.VISA_Inventory) not like "" AND  (tbl_released_drawing.Inventory_Impact like "TO BE SCRAPPED" OR tbl_released_drawing.Inventory_Impact like "TO BE UPDATED"))
	OR  (TRIM(tbl_released_drawing.VISA_Inventory) like "" AND (tbl_released_drawing.Inventory_Impact like "NO IMPACT" OR tbl_released_drawing.Doc_Type like "DOC"))
	)
	
	
) 

AND TRIM(VISA_GID) like ""
';

// $GID_2_Conditions='
// (
// ((tbl_released_drawing.VISA_PUR_1 not like "" AND tbl_released_drawing.Proc_Type like "F") 
// OR (tbl_released_drawing.VISA_PUR_2 not like "" AND tbl_released_drawing.Proc_Type like "F30")
// OR (tbl_released_drawing.VISA_Quality not like "" AND tbl_released_drawing.Proc_Type like "E" AND tbl_released_drawing.VISA_Supply not like "")
// )

// AND (
	// (tbl_released_drawing.VISA_Inventory not like "" AND (tbl_released_drawing.Inventory_Impact like "TO BE SCRAPPED" OR tbl_released_drawing.Inventory_Impact like "TO BE UPDATED"))
// OR (tbl_released_drawing.VISA_Inventory like "" AND tbl_released_drawing.Inventory_Impact like "NO IMPACT")
// )
// ) AND VISA_GID_2 like "" AND VISA_GID not like ""';

$GID_2_Conditions='
	TRIM(tbl_released_drawing.VISA_GID) not like ""
AND TRIM(tbl_released_drawing.VISA_GID_2) like ""
';


$ROUTING_ENTRY_Conditions='
(
	( 
		tbl_released_drawing.Doc_Type like "ASSY"
	AND TRIM(tbl_released_drawing.VISA_MOF) not like ""
	) 
	OR
	( 
		(tbl_released_drawing.Doc_Type like "MOLD" OR tbl_released_drawing.Doc_Type like "MACH")
	AND TRIM(tbl_released_drawing.VISA_PROD) not like ""
	) 

)
AND TRIM(tbl_released_drawing.VISA_GID_2) not like ""
AND TRIM(tbl_released_drawing.VISA_ROUTING_ENTRY)like ""
';

?>