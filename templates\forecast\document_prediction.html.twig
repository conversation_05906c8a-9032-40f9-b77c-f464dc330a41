{% extends 'base.html.twig' %}

{% block title %}Prédiction pour le document {{ document.reference }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .prediction-header {
        background-color: #f0f4f8;
        border-radius: 4px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #0275d8;
    }

    .prediction-card {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
    }

    .prediction-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .card-header {
        background-color: #f8f9fa;
        color: #495057;
        padding: 12px 15px;
        border-radius: 4px 4px 0 0;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
    }

    .card-body {
        padding: 20px;
    }

    .document-info {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .document-info-item {
        margin-bottom: 12px;
        display: flex;
        flex-direction: column;
    }

    .document-info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 3px;
    }

    .document-info-value {
        font-weight: 500;
        color: #212529;
    }

    .prediction-value {
        font-size: 2.5rem;
        font-weight: 600;
        color: #0275d8;
        text-align: center;
        margin: 20px 0;
        line-height: 1.2;
    }

    .prediction-range {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 30px 0;
    }

    .prediction-min,
    .prediction-max {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .prediction-min {
        color: #28a745;
    }

    .prediction-max {
        color: #dc3545;
    }

    .prediction-bar {
        height: 8px;
        background: linear-gradient(to right, #28a745, #ffc107, #dc3545);
        border-radius: 4px;
        margin: 0 15px;
        flex-grow: 1;
        position: relative;
    }

    .prediction-marker {
        position: absolute;
        top: -6px;
        width: 20px;
        height: 20px;
        background-color: #0275d8;
        border-radius: 50%;
        transform: translateX(-50%);
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .prediction-confidence {
        text-align: center;
        margin-top: 20px;
        font-size: 1rem;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .similar-documents {
        margin-top: 30px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }

    .similar-documents-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 12px;
        color: #495057;
    }

    .similar-document-count {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
    }

    .similar-document-count i {
        margin-right: 8px;
        color: #0275d8;
    }

    .alert {
        border-radius: 4px;
        border-left-width: 4px;
    }

    .btn-outline-primary, .btn-outline-secondary {
        border-radius: 4px;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="prediction-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-chart-line text-primary me-2"></i>
                Prédiction pour le document
            </h1>
            <p class="text-muted mb-0">{{ document.reference }} - {{ document.refTitleFra|default('Sans titre') }}</p>
        </div>
        <div>
            <a href="{{ path('app_forecast') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Retour aux prévisions
            </a>
            <a href="{{ path('app_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-tachometer-alt"></i> Tableau de bord
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="prediction-card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i>
                    Informations sur le document
                </div>
                <div class="card-body">
                    <div class="document-info">
                        <div class="document-info-item">
                            <div class="document-info-label">Référence</div>
                            <div class="document-info-value">{{ document.reference }}</div>
                        </div>
                        <div class="document-info-item">
                            <div class="document-info-label">Titre</div>
                            <div class="document-info-value">{{ document.refTitleFra|default('N/A') }}</div>
                        </div>
                        <div class="document-info-item">
                            <div class="document-info-label">Type de document</div>
                            <div class="document-info-value">{{ document.docType|default('N/A') }}</div>
                        </div>
                        <div class="document-info-item">
                            <div class="document-info-label">Type de processus</div>
                            <div class="document-info-value">{{ document.procType|default('N/A') }}</div>
                        </div>
                        <div class="document-info-item">
                            <div class="document-info-label">Type de matériau</div>
                            <div class="document-info-value">{{ document.materialType|default('N/A') }}</div>
                        </div>
                        <div class="document-info-item">
                            <div class="document-info-label">État actuel</div>
                            <div class="document-info-value">
                                {% set currentSteps = document.currentSteps %}
                                {% if currentSteps|length > 0 %}
                                    {{ currentSteps|keys|first }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="similar-documents">
                        <div class="similar-documents-title">Documents similaires</div>
                        <div class="similar-document-count">
                            <i class="fas fa-file-alt me-2"></i>
                            {{ prediction.sample_size }} documents similaires trouvés
                        </div>
                        <p class="text-muted">
                            La prédiction est basée sur l'analyse de documents similaires ayant les mêmes caractéristiques (type de document, type de processus, type de matériau).
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="prediction-card">
                <div class="card-header">
                    <i class="fas fa-chart-line me-2"></i>
                    Prédiction du temps de traitement
                </div>
                <div class="card-body">
                    {% if prediction.avg_time is not null %}
                        <div class="prediction-value">
                            {{ prediction.avg_time }} jours
                        </div>

                        <p class="text-center">
                            Temps de traitement prévu pour ce document, basé sur l'analyse de documents similaires.
                        </p>

                        {% if prediction.min_time is not null and prediction.max_time is not null %}
                            <div class="prediction-range">
                                <div class="prediction-min">{{ prediction.min_time }} jours</div>
                                <div class="prediction-bar">
                                    {% set range = prediction.max_time - prediction.min_time %}
                                    {% set position = range > 0 ? ((prediction.avg_time - prediction.min_time) / range) * 100 : 50 %}
                                    <div class="prediction-marker" style="left: {{ position }}%;"></div>
                                </div>
                                <div class="prediction-max">{{ prediction.max_time }} jours</div>
                            </div>

                            <div class="prediction-confidence">
                                <i class="fas fa-info-circle me-2"></i>
                                La plage de temps estimée est de <strong>{{ prediction.min_time }} à {{ prediction.max_time }} jours</strong>.
                            </div>
                        {% endif %}

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Conseil :</strong> Basé sur l'analyse des documents similaires, ce document devrait prendre environ {{ prediction.avg_time }} jours à traiter.
                            {% if prediction.avg_time > 30 %}
                                C'est un temps de traitement relativement long. Vous pourriez envisager de suivre ce document de près pour éviter les retards.
                            {% elseif prediction.avg_time > 15 %}
                                C'est un temps de traitement moyen. Assurez-vous de suivre les étapes du workflow en temps opportun.
                            {% else %}
                                C'est un temps de traitement relativement court. Ce document devrait progresser rapidement dans le workflow.
                            {% endif %}
                        </div>

                        {% set currentSteps = document.currentSteps %}
                        {% if currentSteps|length > 0 %}
                            {% set currentState = currentSteps|keys|first %}
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>État actuel :</strong> {{ currentState }}
                                <br>
                                <small>
                                    {% set rawTimestamps = document.rawStateTimestamps %}
                                    {% if rawTimestamps[currentState] is defined %}
                                        {% set entries = rawTimestamps[currentState] %}
                                        {% if entries is iterable %}
                                            {% set lastEntry = entries|last %}
                                            {% set enterDate = date(lastEntry.enter) %}
                                            {% set daysSince = date().diff(enterDate).days %}
                                            Ce document est dans cet état depuis {{ daysSince }} jours.
                                            {% if daysSince > 7 %}
                                                <strong>Attention :</strong> Ce document stagne dans cet état.
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                </small>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Impossible de faire une prédiction pour ce document. Pas assez de documents similaires trouvés.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
