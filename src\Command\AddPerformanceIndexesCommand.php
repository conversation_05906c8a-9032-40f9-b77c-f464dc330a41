<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:add-performance-indexes',
    description: 'Ajoute les index de performance pour optimiser les requêtes de base de données'
)]
class AddPerformanceIndexesCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Simuler les changements sans les appliquer')
            ->addOption('analyze', null, InputOption::VALUE_NONE, 'Analyser les performances actuelles');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');

        $io->title('Ajout des index de performance');

        if ($input->getOption('analyze')) {
            $this->analyzeCurrentPerformance($io);
        }

        $this->addPerformanceIndexes($io, $dryRun);

        $io->success('Optimisation des index terminée.');
        return Command::SUCCESS;
    }

    private function analyzeCurrentPerformance(SymfonyStyle $io): void
    {
        $io->section('Analyse des performances actuelles');

        try {
            // Statistiques de base
            $totalDocuments = $this->connection->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();
            $documentsWithSteps = $this->connection->executeQuery(
                "SELECT COUNT(*) FROM document WHERE current_steps IS NOT NULL AND current_steps != '{}'"
            )->fetchOne();
            $documentsWithTimestamps = $this->connection->executeQuery(
                "SELECT COUNT(*) FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''"
            )->fetchOne();
            $totalVisas = $this->connection->executeQuery("SELECT COUNT(*) FROM visa")->fetchOne();

            $io->table(
                ['Métrique', 'Valeur'],
                [
                    ['Total documents', number_format($totalDocuments)],
                    ['Documents avec étapes', number_format($documentsWithSteps)],
                    ['Documents avec timestamps', number_format($documentsWithTimestamps)],
                    ['Total visas', number_format($totalVisas)],
                ]
            );

            // Analyser les index existants
            $io->section('Index existants sur la table document');
            $indexes = $this->connection->executeQuery("SHOW INDEX FROM document")->fetchAllAssociative();
            
            $indexTable = [];
            foreach ($indexes as $index) {
                $indexTable[] = [
                    $index['Key_name'],
                    $index['Column_name'],
                    $index['Index_type'],
                    $index['Cardinality'] ?? 'N/A'
                ];
            }
            
            $io->table(['Nom', 'Colonne', 'Type', 'Cardinalité'], $indexTable);

        } catch (\Exception $e) {
            $io->error('Erreur lors de l\'analyse: ' . $e->getMessage());
        }
    }

    private function addPerformanceIndexes(SymfonyStyle $io, bool $dryRun): void
    {
        $io->section('Ajout des index de performance');

        $indexQueries = [
            // Index pour current_steps JSON - les plus importants
            "CREATE INDEX IF NOT EXISTS idx_current_steps_costing ON document ((JSON_EXTRACT(current_steps, '$.Costing')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_quality ON document ((JSON_EXTRACT(current_steps, '$.Quality')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_assembly ON document ((JSON_EXTRACT(current_steps, '$.Assembly')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_machining ON document ((JSON_EXTRACT(current_steps, '$.Machining')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_planning ON document ((JSON_EXTRACT(current_steps, '$.Planning')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be_0 ON document ((JSON_EXTRACT(current_steps, '$.BE_0')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be_1 ON document ((JSON_EXTRACT(current_steps, '$.BE_1')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be ON document ((JSON_EXTRACT(current_steps, '$.BE')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_produit ON document ((JSON_EXTRACT(current_steps, '$.Produit')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_qual_logistique ON document ((JSON_EXTRACT(current_steps, '$.Qual_Logistique')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_logistique ON document ((JSON_EXTRACT(current_steps, '$.Logistique')))",
            
            // Index pour state_timestamps
            "CREATE INDEX IF NOT EXISTS idx_state_timestamps_not_null ON document (state_timestamps) WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''",
            
            // Index composites pour les requêtes fréquentes
            "CREATE INDEX IF NOT EXISTS idx_document_supervisor_timestamps ON document (superviseur_id, state_timestamps) WHERE state_timestamps IS NOT NULL",
            "CREATE INDEX IF NOT EXISTS idx_visa_released_drawing_name_status ON visa (released_drawing_id, name, status)",
            "CREATE INDEX IF NOT EXISTS idx_visa_validator_date ON visa (validator_id, date_visa)",
            
            // Index pour les champs fréquemment utilisés
            "CREATE INDEX IF NOT EXISTS idx_document_reference_rev ON document (reference, ref_rev)",
            "CREATE INDEX IF NOT EXISTS idx_document_material ON document (material)",
            "CREATE INDEX IF NOT EXISTS idx_document_proc_type ON document (proc_type)",
            "CREATE INDEX IF NOT EXISTS idx_document_doc_type ON document (doc_type)",
            "CREATE INDEX IF NOT EXISTS idx_document_pris_dans1 ON document (pris_dans1)",
        ];

        $progressBar = $io->createProgressBar(count($indexQueries));
        $progressBar->start();

        $successCount = 0;
        $errorCount = 0;

        foreach ($indexQueries as $query) {
            try {
                if (!$dryRun) {
                    $this->connection->executeStatement($query);
                }
                $successCount++;
                
                if ($dryRun) {
                    $io->writeln("\n[DRY RUN] Exécuterait: " . substr($query, 0, 80) . "...");
                }
            } catch (\Exception $e) {
                $errorCount++;
                $io->writeln("\n<error>Erreur lors de l'exécution: " . $e->getMessage() . "</error>");
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $io->newLine(2);

        if ($dryRun) {
            $io->info("Mode simulation: {$successCount} index seraient créés, {$errorCount} erreurs détectées.");
        } else {
            $io->success("{$successCount} index créés avec succès, {$errorCount} erreurs.");
        }

        // Analyser l'impact des nouveaux index
        if (!$dryRun && $successCount > 0) {
            $io->section('Analyse de l\'impact des nouveaux index');
            
            try {
                // Vérifier la taille des index
                $indexSizes = $this->connection->executeQuery("
                    SELECT 
                        INDEX_NAME,
                        ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'Index Size (MB)'
                    FROM information_schema.STATISTICS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'document'
                    AND INDEX_NAME LIKE 'idx_%'
                    GROUP BY INDEX_NAME
                    ORDER BY INDEX_LENGTH DESC
                ")->fetchAllAssociative();
                
                if (!empty($indexSizes)) {
                    $io->table(['Nom de l\'index', 'Taille (MB)'], $indexSizes);
                }
            } catch (\Exception $e) {
                $io->warning('Impossible d\'analyser la taille des index: ' . $e->getMessage());
            }
        }
    }
}
