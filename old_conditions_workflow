<?php

return [
    // 'BE_1' => [
    //     'criteria' => [
    //         'creation_visa' => 'forbidden',
    //         'visa_be_2' => 'forbidden',
    //         'visa_be_3' => 'forbidden',
    //     ]
    // ],
    // 'BE_2' => [
    //     'criteria' => [
    //         'visa_be_2' => 'forbidden',
    //         'visa_be_3' => 'forbidden',
    //         'creation_visa' => 'required',
    //     ]
    // ],
    // 'BE_3' => [
    //     'criteria' => [
    //         'visa_be_3' => 'forbidden',
    //         'creation_visa' => 'required',
    //         'visa_be_2' => 'required',
    //     ]
    // ],
    // 'Project' => [
    //     'criteria' => [
            // 'visa_project' => 'forbidden',
            // 'project' => ['not_like' => 'STAND'],
            // 'visa_be_3' => 'required',
            // 'prod_draw' => ['begin_with' => ['GA%', 'FT%']],
    //     ]
    // ],
    // 'Quality' => [
    //     'criteria' => [
        // 'visa_quality' => 'forbidden',
    //         'doc_type' => ['PUR', 'ASSY', 'DOC'],
            // 'visa_be_3' => 'required',
    //     ]
    // ],
    // 'Product' => [
    //     'criteria' => [
    //         'visa_product' => 'forbidden',
    //         'visa_be_3' => 'required',
    //     ]
    // ],
    
    // 'Inventory' => [
    //     'criteria' => [
            // 'visa_inventory' => 'forbidden',
            // 'visa_be_3' => 'required',
            // 'visa_gid' => 'forbidden',
            // 'doc_type' => ['not_like' => 'DOC'],
            // 'inventory_impact' => ['not_like' => 'NO IMPACT'],
    //     ]
    // ],
    // assemblage
    // 'Prod_ASSY' => [ 
    //     'criteria' => [
    //         'visa_prod' => 'forbidden',
    //         'visa_product' => 'required',
    //         'doc_type' => ['ASSY', 'DOC'],
    //         if($doc_type == 'ASSY') {
    //             'proc_type' => ['not_like' => 'F'],
    //         }
    //     ]
    // ],
    
    // usinage
    // 'Prod_MACH' => [ 
    //     'criteria' => [
    //         'visa_prod' => 'forbidden',
    //         'visa_product' => 'required',
    //         'doc_type' => 'MACH',
    //     ]
    // ],
    // moulage
    // 'Prod_MOLD' => [ 
    //     'criteria' => [
    //         'visa_prod' => 'forbidden',
    //         'visa_product' => 'required',
    //         'doc_type' => 'MOLD',
    //     ]
    // ],
    // methodologie assemblage
    // 'Method' => [ 
    //     'criteria' => [
    //         'visa_method' => 'forbidden',
    //         'doc_type' => ['ASSY', 'DOC'],
    //         'material_type' => ['optional' => 'PACKAGING'],
    //         'visa_prod' => 'required',
    //     ]
    // ],
    // planning
    'Supply' => [
        'criteria' => [
            'visa_supply' => 'forbidden',
            'doc_type' => ['ASSY', 'MACH', 'MOLD', 'DOC'],
            'visa_prod' => 'required',
        ]
    ],
    // RFQ
    // ENZO WARNING
    // 'PUR_1_RFQ' => [ 
    //     'criteria' => [
    //         'visa_pur_1' => 'forbidden',
    //         'doc_type' => 'PUR',
    //         'proc_type' => ['optional' => ['', 'F', 'F30']],
    //         'visa_quality' => 'required',
    //         'visa_product' => 'required',
    //     ]
    // ],
    // F30
    // 'PUR_2_PRISDANS' => [
    //     'criteria' => [
    //         'visa_pur_2' => 'forbidden',
    //         'visa_pur_1' => 'required',
    //         'proc_type' => 'F30',
    //     ]
    // ],
    // FIA
    // 'PUR_3' => [
    //     'criteria' => [
    //         'visa_pur_3' => 'forbidden',
    //         'visa_pur_1' => 'required',
    //         'proc_type' => ['F', 'F30'],
    //         'visa_gid_2' => 'required',
    //     ]
    // ],
    // ROHS:REACH
    // 'PUR_4' => [
    //     'criteria' => [
    //         'visa_pur_4' => 'forbidden',
    //         'visa_pur_1' => 'required',
    //     ]
    // ],
    // HTS
    // 'PUR_5' => [
    //     'criteria' => [
    //         'visa_pur_5' => 'forbidden',
    //         'visa_pur_3' => 'required',
    //     ]
    // ],
    // au moins l'une des 2 conditions
    // 'METRO' => [ 
    //     'criteria' => [
    //         'visa_metro' => 'forbidden',
    //         'condition_1' => [
    //             'visa_prod' => 'required',
    //             'proc_type' => 'E',
    //         ],
    //         'condition_2' => [
    //             'visa_quality' => 'required',
    //             'doc_type' => 'PUR',
    //         ],
    //     ]
    // ],

    // 'Q_PROD' => [
    //     'criteria' => [
    //         'visa_q_prod' => 'forbidden',
    //         'visa_metro' => 'required',
    //         'doc_type' => ['MACH', 'MOLD'],
    //     ]
    // ],
    // 'LABO' => [
    //     'criteria' => [
    //         'visa_labo' => 'forbidden',
    //         'doc_type' => 'ASSY',
    //         'visa_mof' => 'required',
    //     ]
    // ],
    // au moins l'une des 2 conditions
    // INDUS
    'MOF' => [ 
        'criteria' => [
            'visa_mof' => 'forbidden',
            'condition_1' => [
                'doc_type' => 'ASSY',
                'visa_metro' => 'required',
            ],
            'condition_2' => [
                'doc_type' => 'DOC',
            ],
            'visa_prod' => 'required',
        ]
    ],
    // Costing
    // 'Finance' => [ 
    //     'criteria' => [
    //         'visa_finance' => 'forbidden',
    //         'doc_type' => ['not' => 'DOC'],
    //         'to_Costing_from_Achat_FIA' => [
    //             'visa_pur_3' => 'required',
    //             'doc_type' => 'PUR',
    //         ],
    //         'to_Costing' => [
    //             'visa_routing_entry' => 'required',
    //             'doc_type' => ['not' => 'PUR'],
    //         ],
    //     ]
    // ],
    // Core Data 
    'GID_1' => [ 
        'criteria' => [
            'visa_gid' => 'forbidden',
            'condition_1' => [
                'visa_pur_1' => 'required',
                'proc_type' => 'F',
                'doc_type' => 'PUR',
            ],
            'condition_2' => [
                'visa_pur_2' => 'required',
                'proc_type' => 'F30',
                'doc_type' => 'PUR',
            ],
            'condition_3' => [
                'doc_type' => ['MACH', 'MOLD'],
                'visa_metro' => 'required',
                'visa_supply' => 'required',
            ],
            'condition_4' => [
                'doc_type' => 'ASSY',
                'visa_quality' => 'required',
                'visa_metro' => 'required',
                'visa_supply' => 'required',
                'sub_conditions' => [
                    'sub_condition_1' => [
                        'project' => ['not' => 'STAND'],
                        'visa_project' => 'required',
                        'prod_draw' => ['begin' => ['GA', 'FT']],
                    ],
                    'sub_condition_2' => [
                        'project' => ['not' => 'STAND'],
                        'visa_project' => 'forbidden',
                        'prod_draw' => ['not_begin' => ['GA', 'FT']],
                    ],
                    'sub_condition_3' => [
                        'project' => 'STAND',
                        'visa_project' => 'forbidden',
                    ],
                ],
            ],
            'condition_5' => [
                'doc_type' => 'DOC',
                'visa_quality' => 'required',
                'visa_metro' => 'required',
                'sub_conditions' => [
                    'sub_condition_1' => [
                        'project' => ['not' => 'STAND'],
                        'visa_project' => 'required',
                        'prod_draw' => ['like' => ['GA%', 'FT%']],
                    ],
                    'sub_condition_2' => [
                        'project' => ['not' => 'STAND'],
                        'visa_project' => 'forbidden',
                        'prod_draw' => ['not_like' => ['GA%', 'FT%']],
                    ],
                    'sub_condition_3' => [
                        'project' => 'STAND',
                        'visa_project' => 'forbidden',
                    ],
                ],
            ],
            'inventory_conditions' => [
                'inventory_condition_1' => [
                    'visa_inventory' => 'required',
                    'inventory_impact' => ['TO BE SCRAPPED', 'TO BE UPDATED'],
                ],
                'inventory_condition_2' => [
                    'visa_inventory' => 'forbidden',
                    'inventory_impact' => ['NO IMPACT', 'DOC'],
                ],
            ],
        ]
    ],
    // Prod data
    // 'GID_2' => [
    //     'criteria' => [
    //         'visa_gid_2' => 'forbidden',
    //         'visa_gid' => 'required',
    //     ]
    // ],
    // GID
    'ROUTING_ENTRY' => [ 
        'criteria' => [
            'visa_routing_entry' => 'forbidden',
            'condition_1' => [
                'doc_type' => 'ASSY',
                'visa_mof' => 'required',
            ],
            'condition_2' => [
                'doc_type' => ['MOLD', 'MACH'],
                'visa_prod' => 'required',
            ],
            'visa_gid_2' => 'required',
        ]
    ],
];

?>
