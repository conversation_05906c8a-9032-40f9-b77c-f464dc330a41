function validateField(input, regex, documentId, field, rulesMessage) {
    const value = input.value.trim();
    if (!regex.test(value)) {
        input.classList.add('is-invalid');
        Toast.fire({
            icon: 'error',
            title: `Le champ "${field}" n'est pas valide.`,
            text: `Règle : ${rulesMessage}`
        });
        return;
    }
    input.classList.remove('is-invalid');
    updateDocumentField(documentId, field, value);
}

// Tri au clic sur l'entête
function sortTable() {
    var table = $(this).closest('table');
    var th = $(this);
    var index = th.index();
    var tbody = table.find('tbody');

    var tr = tbody.find('tr').toArray().sort(function(a, b){
        var aText = $(a).find('td').eq(index).text();
        var bText = $(b).find('td').eq(index).text();

        if ($(a).find('td').eq(index).attr('date')) {
            aText = $(a).find('td').eq(index).attr('date');
        }
        if ($(b).find('td').eq(index).attr('date')) {
            bText = $(b).find('td').eq(index).attr('date');
        }

        if ($(a).find('td').eq(index).find('.badge[week]').length) {
            aText = $(a).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
        }
        if ($(b).find('td').eq(index).find('.badge[week]').length) {
            bText = $(b).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
        }

        return aText.localeCompare(bText);
    });

    if(th.hasClass('sort-asc')) {
        th.removeClass('sort-asc');
        th.addClass('sort-desc');
        tr = tr.reverse();
        $('th').find('i').remove();
        th.append('<i class="fas fa-sort-up"></i>');
    } else {
        th.removeClass('sort-desc');
        th.addClass('sort-asc');
        $('th').find('i').remove();
        th.append('<i class="fas fa-sort-down"></i>');
    }
    tbody.empty();
    tbody.append(tr);
}

function refreshTable(){
    $.ajax({
        url: window.location.href,
        type: 'GET',
        success: function(data){
            var table = $(data).find('#table');
            $('#table-container').html(table);
        }
    });
}

// --- Recherche par colonne ---
function filterTableByColumn() {
    var rows = $('#table tbody tr');

    rows.each(function() {
        var showRow = true;
        var row = $(this);

        row.find('td').each(function(index, td) {
            var inputSearch = $('input[data-col="' + index + '"]');
            if (inputSearch.length > 0) {
                var searchVal = inputSearch.val().toLowerCase().trim();
                var cellText = $(td).text().toLowerCase().trim();

                if (searchVal !== '' && cellText.indexOf(searchVal) === -1) {
                    showRow = false;
                    return false; 
                }
            }
        });

        if (showRow) {
            row.show();
        } else {
            row.hide();
        }
    });
}

$(document).on('click', '#entetes th', sortTable);

$(document).on('keyup change', 'thead input', function() {
    filterTableByColumn();
});

let debounceTimers = {};
let docTypeDisplayName = {
    'ASSY': 'Assemblage',
    'MACH': 'Usinage',
    'MOLD': 'Moulage',
    'DOC':  'Document',
    'PUR':  'Achat'
};

function updateDocumentField(documentId, field, value) {
    if (debounceTimers[documentId + field]) {
        clearTimeout(debounceTimers[documentId + field]);
    }
    debounceTimers[documentId + field] = setTimeout(() => {
        if (field !== 'doctype') {
            sendUpdateRequest(documentId, field, value);
        } else {
            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Le document arrivera dans le département " + docTypeDisplayName[value] + ".",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, changer !'
            }).then((result) => {
                if (result.isConfirmed) {
                    sendUpdateRequest(documentId, field, value);
                }
            });
        }
    }, 300);
}

function sendUpdateRequest(documentId, field, value) {
    $.ajax({
        url: "{{ path('update_document') }}",
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ id: documentId, field: field, value: value }),
        success: function(data) {
            if (data.status === 'success') {
                console.log('Mise à jour réussie pour:', field);
                Toast.fire({
                    icon: 'success',
                    title: 'Mise à jour réussie'
                });
            } else {
                alert('Erreur: ' + data.message);
            }
        },
        error: function(error) {
            console.error('Erreur lors de la mise à jour:', error);
        }
    });
}

function createVisa(documentId, currentsteps) {
    let name = 'Qual_Logistique';
    if (Array.isArray(currentsteps) && currentsteps.includes('Qual_Logistique')) {
        name = 'Logistique';
    }
    const Visadata = {
        documentId: documentId,
        name: name
    };

    $.ajax({
        url: "{{ path('create_visa') }}",
        type: "POST",
        contentType: "application/json",
        data: JSON.stringify(Visadata),
        success: function(result) {
            if (result.status === "success") {
                Toast.fire({
                    icon: "success",
                    title: result.message
                });
                refreshTable();
            } else {
                Toast.fire({
                    icon: "error",
                    title: "Erreur lors de la création du visa",
                    text: result.message
                });
            }
        },
        error: function(xhr) {
            // Extraire le message d'erreur du backend
            const errorResponse = xhr.responseJSON;

            if (errorResponse && errorResponse.message === "visa valid existant") {
                Toast.fire({
                    icon: "warning",
                    title: "Visa déjà créé",
                    text: "Un visa pour ce document existe déjà."
                });
            } else if (errorResponse && errorResponse.message) {
                Toast.fire({
                    icon: "error",
                    title: "Erreur",
                    text: errorResponse.message
                });
            } else {
                Toast.fire({
                    icon: "error",
                    title: "Une erreur s'est produite",
                    text: "Veuillez vérifier les données ou réessayer plus tard."
                });
            }
        }
    });
}

function showVisas(documentId) {
    console.log(documentId);
    $.ajax({
        url: '{{ path("document_visas", {"id": "ID_PLACEHOLDER"}) }}'.replace('ID_PLACEHOLDER', documentId),
        type: 'GET',
        success: function(data) {
            let modalBody = document.querySelector('#modalVisas .modal-body');
            modalBody.innerHTML = '';
            if (data.length === 0) {
                modalBody.innerHTML = '<p>Aucun visa pour ce document.</p>';
            } else {
                let table = '<table class="table table-sm table-bordered">' +
                            '<thead><tr>' +
                            '<th>Nom du visa</th>' +
                            '<th>Signé par</th>' +
                            '<th>Date de signature</th>' +
                            '</tr></thead>' +
                            '<tbody>';
                data.forEach(visa => {
                    table += '<tr>' +
                            '<td>' + visa.name + '</td>' +
                            '<td>' + visa.signer + '</td>' +
                            '<td>' + visa.dateVisa + '</td>' +
                            '</tr>';
                });
                table += '</tbody></table>';
                modalBody.innerHTML = table;
            }

            // On ouvre la modale
            let myModal = new bootstrap.Modal(document.getElementById('modalVisas'));
            myModal.show();
        },
        error: function(err) {
            alert("Erreur lors de la récupération des visas");
        }
    });
}

