# Explication du fonctionnement des prévisions de temps de traitement

## Vue d'ensemble

Le système de prévisions utilise une approche statistique basée sur l'analyse des données historiques pour prédire les temps de traitement futurs. Voici comment cela fonctionne :

## 1. Collecte des données historiques

### Source des données
- **Documents analysés** : Uniquement les documents **terminés** (qui ont le visa correspondant à leur état ou sont dans un état panier)
- **Période d'analyse** : 12 derniers mois par défaut
- **Granularité** : Mensuelle, trimestrielle ou annuelle

### Calcul du temps de traitement
Pour chaque document terminé :
```php
// Calcul basé sur les timestamps d'état
$firstDate = null; // Première date d'entrée dans le workflow
$lastDate = null;  // Dernière date de sortie du workflow

// Le temps total = différence entre première et dernière date
$processingTime = $lastDate->diff($firstDate)->days;
```

## 2. Analyse des tendances

### Moyennes mobiles
Le système calcule une **moyenne mobile sur 3 périodes** pour lisser les variations :
```php
for ($i = 2; $i < count($historicalData); $i++) {
    $movingAverage[] = ($historicalData[$i] + $historicalData[$i-1] + $historicalData[$i-2]) / 3;
}
```

### Régression linéaire
Une **tendance linéaire** est calculée sur les moyennes mobiles :
- **Pente (slope)** : Indique si les temps augmentent ou diminuent
- **Ordonnée à l'origine (intercept)** : Point de départ de la tendance

```php
$slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
$intercept = ($sumY - $slope * $sumX) / $n;
```

## 3. Génération des prévisions

### Formule de prédiction
Pour chaque mois futur :
```php
$predictedValue = $intercept + $slope * $nextX;
```

### Contraintes
- **Valeur minimale** : 0 (pas de temps négatif)
- **Période** : 3 mois dans le futur
- **Arrondi** : 1 décimale

## 4. Pourquoi les valeurs peuvent sembler courtes

### Facteurs explicatifs possibles :

#### A. **Données incomplètes**
- Si peu de documents sont réellement "terminés" selon les nouveaux critères
- Les documents en cours faussent les statistiques s'ils sont inclus

#### B. **Calcul du temps total**
Le système actuel calcule le temps entre la **première** et **dernière** date d'état, mais :
- Il peut y avoir des **gaps** dans les timestamps
- Certains états peuvent être **parallèles** (non séquentiels)
- Les **retours en arrière** ne sont pas comptabilisés correctement

#### C. **Méthode de calcul des états**
```php
// Méthode actuelle dans ForecastController
foreach ($rawTimestamps as $state => $entries) {
    $stateDays = $document->getTotalDaysInState($state);
    if ($stateDays !== null) {
        $totalDays += $stateDays; // ADDITION de tous les états
    }
}
```
**Problème** : Cette méthode **additionne** le temps passé dans chaque état, ce qui peut donner des résultats incorrects si :
- Les états se chevauchent
- Un document passe plusieurs fois par le même état
- Il y a des états parallèles

#### D. **Filtrage récent**
Avec les nouvelles modifications, seuls les documents **réellement terminés** sont analysés, ce qui peut :
- Réduire drastiquement l'échantillon de données
- Exclure des documents qui prenaient plus de temps
- Biaiser vers des documents traités plus rapidement

## 5. Recommandations pour améliorer les prévisions

### A. **Vérifier la méthode de calcul**
```php
// Méthode recommandée : temps total du workflow
$firstDate = null;
$lastDate = null;

foreach ($timestamps as $state => $entries) {
    if (is_array($entries)) {
        foreach ($entries as $entry) {
            if (isset($entry['enter'])) {
                $enterDate = new \DateTime($entry['enter']);
                if ($firstDate === null || $enterDate < $firstDate) {
                    $firstDate = $enterDate;
                }
            }
            if (isset($entry['exit']) && $entry['exit'] !== null) {
                $exitDate = new \DateTime($entry['exit']);
                if ($lastDate === null || $exitDate > $lastDate) {
                    $lastDate = $exitDate;
                }
            }
        }
    }
}

// Temps total = différence entre première entrée et dernière sortie
if ($firstDate && $lastDate) {
    $totalTime = $lastDate->diff($firstDate)->days;
}
```

### B. **Analyser les données**
- Vérifier combien de documents sont considérés comme "terminés"
- Examiner la distribution des temps de traitement
- Identifier les outliers (documents exceptionnellement rapides/lents)

### C. **Améliorer l'algorithme**
- Utiliser une **régression polynomiale** au lieu de linéaire
- Implémenter une **pondération** par récence des données
- Ajouter des **facteurs saisonniers**
- Considérer les **types de documents** séparément

### D. **Validation des prévisions**
- Comparer les prévisions passées avec la réalité
- Calculer l'**erreur moyenne** des prédictions
- Ajuster les paramètres en conséquence

## 6. Diagnostic recommandé

Pour comprendre pourquoi les valeurs sont courtes, examinez :

1. **Nombre de documents terminés** dans l'échantillon
2. **Distribution des temps** (min, max, médiane, moyenne)
3. **Cohérence des timestamps** dans la base de données
4. **Logique de détermination** des documents terminés
5. **Méthode de calcul** du temps total par document

## 7. Code de diagnostic suggéré

```php
// Dans ForecastController, ajouter des logs de diagnostic
$totalDocuments = count($documents);
$completedCount = count($completedDocuments);
$avgTime = array_sum(array_column($docTypeStats, 'avg_time')) / count($docTypeStats);

error_log("Forecast Debug: Total={$totalDocuments}, Completed={$completedCount}, AvgTime={$avgTime}");
```

Cette analyse devrait vous aider à identifier pourquoi les prévisions semblent courtes et comment les améliorer.
