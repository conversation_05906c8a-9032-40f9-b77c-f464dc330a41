<?php
// Script pour générer des données de test pour les tables vides

require_once __DIR__ . '/vendor/autoload.php';

use App\Entity\Code;
use App\Entity\Commentaire;
use App\Entity\Config;
use App\Entity\DMO;
use App\Entity\Document;
use App\Entity\Impute;
use App\Entity\Phase;
use App\Entity\ProductRange;
use App\Entity\Project;
use App\Entity\ReleasedPackage;
use App\Entity\User;
use App\Repository\DocumentRepository;
use App\Repository\ReleasedPackageRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

// Classe pour exécuter le script
class TestDataGenerator
{
    private EntityManagerInterface $entityManager;
    private array $productRanges = [];
    private array $projects = [];
    private array $phases = [];
    private array $codes = [];
    private array $dmos = [];
    private array $users = [];
    private array $documents = [];
    private array $releasedPackages = [];

    public function __construct(KernelInterface $kernel)
    {
        $container = $kernel->getContainer();
        $this->entityManager = $container->get('doctrine.orm.entity_manager');
        
        // Récupérer les utilisateurs existants
        $this->users = $this->entityManager->getRepository(User::class)->findAll();
        
        // Récupérer les documents existants
        $this->documents = $this->entityManager->getRepository(Document::class)->findAll();
        
        // Récupérer les packages existants
        $this->releasedPackages = $this->entityManager->getRepository(ReleasedPackage::class)->findAll();
    }

    public function execute(): void
    {
        echo "Début de la génération des données de test...\n";

        // Générer les données dans l'ordre des dépendances
        $this->createProductRanges();
        $this->createProjects();
        $this->createPhases();
        $this->createCodes();
        $this->createDMOs();
        $this->createCommentaires();
        $this->createConfig();
        $this->createImputes();

        echo "Génération des données de test terminée avec succès!\n";
    }

    private function createProductRanges(): void
    {
        echo "Création des gammes de produits...\n";
        
        $divisions = ['Aerospace', 'Industrial', 'Energy', 'Medical', 'Automotive'];
        $productRangeNames = [
            'Aerospace' => ['Connectors', 'Cables', 'Harnesses', 'Sensors'],
            'Industrial' => ['Automation', 'Robotics', 'Control Systems', 'Power Distribution'],
            'Energy' => ['Solar', 'Wind', 'Hydro', 'Nuclear'],
            'Medical' => ['Imaging', 'Diagnostics', 'Monitoring', 'Therapy'],
            'Automotive' => ['EV Systems', 'Infotainment', 'Safety', 'Powertrain']
        ];
        
        foreach ($divisions as $division) {
            foreach ($productRangeNames[$division] as $productRangeName) {
                $productRange = new ProductRange();
                $productRange->setDivision($division);
                $productRange->setProductRange($productRangeName);
                
                $this->entityManager->persist($productRange);
                $this->productRanges[] = $productRange;
            }
        }
        
        $this->entityManager->flush();
        echo count($this->productRanges) . " gammes de produits créées.\n";
    }

    private function createProjects(): void
    {
        echo "Création des projets...\n";
        
        $projectTitles = [
            'Développement connecteur haute performance',
            'Amélioration système de câblage',
            'Nouveau capteur de température',
            'Optimisation processus de fabrication',
            'Réduction des coûts de production',
            'Certification ISO 9001',
            'Expansion marché asiatique',
            'Développement produit médical',
            'Intégration système automobile',
            'Projet R&D énergie renouvelable'
        ];
        
        $projectManagers = array_filter($this->users, function($user) {
            return in_array('ROLE_PROJECT_MANAGER', $user->getRoles()) || in_array('ROLE_ADMIN', $user->getRoles());
        });
        
        if (empty($projectManagers)) {
            $projectManagers = $this->users;
        }
        
        foreach ($projectTitles as $index => $title) {
            $project = new Project();
            $project->setTitle($title);
            $project->setOTP('OTP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT));
            
            if (!empty($projectManagers)) {
                $project->setProjectManager($projectManagers[array_rand($projectManagers)]);
            }
            
            $this->entityManager->persist($project);
            $this->projects[] = $project;
        }
        
        $this->entityManager->flush();
        echo count($this->projects) . " projets créés.\n";
    }

    private function createPhases(): void
    {
        echo "Création des phases...\n";
        
        $phaseTitles = [
            'Conception',
            'Développement',
            'Prototypage',
            'Tests',
            'Validation',
            'Production',
            'Déploiement'
        ];
        
        foreach ($this->projects as $project) {
            $level = 1;
            foreach ($phaseTitles as $index => $title) {
                $phase = new Phase();
                $phase->setTitle($title);
                $phase->setCode('PH-' . substr($project->getOTP(), -4) . '-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT));
                $phase->setLevel($level++);
                $phase->setStatus(rand(0, 1) == 1);
                $phase->setStatusManuel(rand(0, 1) == 1);
                $phase->setCommentaire('Commentaire pour la phase ' . $title);
                $phase->setProjet($project);
                
                $this->entityManager->persist($phase);
                $this->phases[] = $phase;
            }
        }
        
        $this->entityManager->flush();
        echo count($this->phases) . " phases créées.\n";
    }

    private function createCodes(): void
    {
        echo "Création des codes...\n";
        
        $codeTitles = [
            'Conception mécanique',
            'Conception électrique',
            'Développement logiciel',
            'Tests fonctionnels',
            'Documentation technique',
            'Gestion de projet',
            'Assurance qualité'
        ];
        
        $workCenters = ['WC001', 'WC002', 'WC003', 'WC004', 'WC005'];
        $statuses = ['Active', 'Inactive', 'Pending', 'Completed'];
        
        foreach ($this->phases as $phase) {
            $level = 1;
            foreach ($codeTitles as $index => $title) {
                $code = new Code();
                $code->setTitle($title);
                $code->setCode('CD-' . $phase->getCode() . '-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT));
                $code->setLevel($level++);
                $code->setStatus($statuses[array_rand($statuses)]);
                $code->setWorkCenter($workCenters[array_rand($workCenters)]);
                $code->setPhase($phase);
                
                $this->entityManager->persist($code);
                $this->codes[] = $code;
            }
        }
        
        $this->entityManager->flush();
        echo count($this->codes) . " codes créés.\n";
    }

    private function createDMOs(): void
    {
        echo "Création des DMOs...\n";
        
        $descriptions = [
            'Modification de conception pour améliorer la performance',
            'Correction d\'un défaut de fabrication',
            'Optimisation du processus de production',
            'Mise à jour de la documentation technique',
            'Changement de matériau pour réduction des coûts',
            'Adaptation aux nouvelles normes réglementaires',
            'Amélioration de la qualité du produit'
        ];
        
        $decisions = ['Approved', 'Rejected', 'Pending', 'Under Review'];
        $types = ['Design', 'Process', 'Material', 'Documentation', 'Quality'];
        
        $engineers = array_filter($this->users, function($user) {
            return in_array('ROLE_ENGINEER', $user->getRoles()) || in_array('ROLE_ADMIN', $user->getRoles());
        });
        
        if (empty($engineers)) {
            $engineers = $this->users;
        }
        
        for ($i = 1; $i <= 50; $i++) {
            $dmo = new DMO();
            $dmo->setDateInit(new \DateTimeImmutable());
            $dmo->setDescription($descriptions[array_rand($descriptions)]);
            $dmo->setDecision($decisions[array_rand($decisions)]);
            $dmo->setStatus(rand(0, 1) == 1);
            $dmo->setEx('EX' . str_pad($i, 4, '0', STR_PAD_LEFT));
            $dmo->setIndusRelated(rand(0, 1) == 1);
            $dmo->setDateEnd(new \DateTime());
            $dmo->setPrNumber(rand(1000, 9999));
            $dmo->setLastUpdateDate(new \DateTime());
            $dmo->setExAssessment('Assessment for DMO #' . $i);
            $dmo->setSpentTime(rand(1, 100));
            $dmo->setType($types[array_rand($types)]);
            $dmo->setDocument('DOC-' . str_pad($i, 4, '0', STR_PAD_LEFT));
            
            // Associer à un utilisateur comme demandeur
            if (!empty($this->users)) {
                $dmo->setRequestor($this->users[array_rand($this->users)]);
            }
            
            // Associer à un ingénieur comme propriétaire
            if (!empty($engineers)) {
                $dmo->setEngOwner($engineers[array_rand($engineers)]);
            }
            
            // Associer à un utilisateur comme dernier modificateur
            if (!empty($this->users)) {
                $dmo->setLastModificator($this->users[array_rand($this->users)]);
            }
            
            // Associer à une gamme de produits
            if (!empty($this->productRanges)) {
                $dmo->setProductRange($this->productRanges[array_rand($this->productRanges)]);
            }
            
            // Associer à un projet
            if (!empty($this->projects)) {
                $dmo->setProjectRelation($this->projects[array_rand($this->projects)]);
            }
            
            // Associer à des packages
            if (!empty($this->releasedPackages)) {
                $numPackages = rand(1, min(3, count($this->releasedPackages)));
                $selectedPackages = array_rand($this->releasedPackages, $numPackages);
                if (!is_array($selectedPackages)) {
                    $selectedPackages = [$selectedPackages];
                }
                
                foreach ($selectedPackages as $packageIndex) {
                    $dmo->addReleasedPackage($this->releasedPackages[$packageIndex]);
                }
            }
            
            $this->entityManager->persist($dmo);
            $this->dmos[] = $dmo;
            
            // Flush tous les 10 DMOs pour éviter de surcharger la mémoire
            if ($i % 10 === 0) {
                $this->entityManager->flush();
            }
        }
        
        $this->entityManager->flush();
        echo count($this->dmos) . " DMOs créés.\n";
    }

    private function createCommentaires(): void
    {
        echo "Création des commentaires...\n";
        
        $commentTexts = [
            'Excellente proposition, je suis d\'accord avec les modifications.',
            'Il faudrait revoir certains aspects techniques avant validation.',
            'Document conforme aux spécifications requises.',
            'Quelques corrections mineures à apporter avant approbation finale.',
            'Validation technique effectuée avec succès.',
            'Des tests supplémentaires sont nécessaires.',
            'Modifications approuvées, prêt pour la prochaine étape.',
            'Révision nécessaire sur les points 3 et 4.',
            'Conforme aux normes ISO en vigueur.',
            'Vérification complète effectuée, aucun problème détecté.'
        ];
        
        $states = ['draft', 'review', 'approved', 'rejected'];
        $types = ['technical', 'quality', 'process', 'general'];
        
        $commentCount = 0;
        
        // Commentaires pour les DMOs
        foreach ($this->dmos as $dmo) {
            $numComments = rand(1, 5);
            
            for ($i = 0; $i < $numComments; $i++) {
                $commentaire = new Commentaire();
                $commentaire->setState($states[array_rand($states)]);
                $commentaire->setCreatedAt(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));
                $commentaire->setCommentaire($commentTexts[array_rand($commentTexts)]);
                $commentaire->setType($types[array_rand($types)]);
                $commentaire->setDmoId($dmo);
                
                if (!empty($this->users)) {
                    $commentaire->setUser($this->users[array_rand($this->users)]);
                }
                
                $this->entityManager->persist($commentaire);
                $commentCount++;
                
                // Flush tous les 20 commentaires
                if ($commentCount % 20 === 0) {
                    $this->entityManager->flush();
                }
            }
        }
        
        // Commentaires pour les Documents
        foreach (array_slice($this->documents, 0, min(50, count($this->documents))) as $document) {
            $numComments = rand(1, 3);
            
            for ($i = 0; $i < $numComments; $i++) {
                $commentaire = new Commentaire();
                $commentaire->setState($states[array_rand($states)]);
                $commentaire->setCreatedAt(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));
                $commentaire->setCommentaire($commentTexts[array_rand($commentTexts)]);
                $commentaire->setType($types[array_rand($types)]);
                $commentaire->setDocuments($document);
                
                if (!empty($this->users)) {
                    $commentaire->setUser($this->users[array_rand($this->users)]);
                }
                
                $this->entityManager->persist($commentaire);
                $commentCount++;
                
                // Flush tous les 20 commentaires
                if ($commentCount % 20 === 0) {
                    $this->entityManager->flush();
                }
            }
        }
        
        $this->entityManager->flush();
        echo $commentCount . " commentaires créés.\n";
    }

    private function createConfig(): void
    {
        echo "Création des configurations...\n";
        
        $configs = [
            ['key' => 'app_name', 'value' => 'FRCM Intranet'],
            ['key' => 'app_version', 'value' => '1.0.0'],
            ['key' => 'maintenance_mode', 'value' => 'false'],
            ['key' => 'default_language', 'value' => 'fr'],
            ['key' => 'email_notification', 'value' => 'true'],
            ['key' => 'max_upload_size', 'value' => '10MB'],
            ['key' => 'document_retention_days', 'value' => '365'],
            ['key' => 'session_timeout', 'value' => '30'],
            ['key' => 'ldap_sync_interval', 'value' => '24'],
            ['key' => 'default_theme', 'value' => 'light']
        ];
        
        foreach ($configs as $configData) {
            $config = new Config();
            $config->setKey($configData['key']);
            $config->setValue($configData['value']);
            
            $this->entityManager->persist($config);
        }
        
        $this->entityManager->flush();
        echo count($configs) . " configurations créées.\n";
    }

    private function createImputes(): void
    {
        echo "Création des imputations...\n";
        
        $imputeCount = 0;
        $users = array_filter($this->users, function($user) {
            return $user->isImputation();
        });
        
        if (empty($users)) {
            $users = array_slice($this->users, 0, min(10, count($this->users)));
            foreach ($users as $user) {
                $user->setImputation(true);
                $this->entityManager->persist($user);
            }
            $this->entityManager->flush();
        }
        
        // Créer des imputations pour les 3 derniers mois
        $months = [
            new \DateTime('first day of -2 month'),
            new \DateTime('first day of -1 month'),
            new \DateTime('first day of this month')
        ];
        
        foreach ($users as $user) {
            foreach ($months as $month) {
                // Chaque utilisateur a entre 5 et 15 imputations par mois
                $numImputes = rand(5, 15);
                $totalHours = 0;
                $maxHours = 160; // Environ 160 heures par mois
                
                for ($i = 0; $i < $numImputes && $totalHours < $maxHours; $i++) {
                    $impute = new Impute();
                    $impute->setUser($user);
                    
                    // Heures restantes ou nombre aléatoire
                    $hours = min(rand(4, 40), $maxHours - $totalHours);
                    $totalHours += $hours;
                    
                    $impute->setNbHeures($hours);
                    
                    // Date dans le mois courant
                    $date = clone $month;
                    $date->modify('+' . rand(0, 27) . ' days');
                    $impute->setCreatedAt($date);
                    
                    // Associer à un code
                    if (!empty($this->codes)) {
                        $impute->setCode($this->codes[array_rand($this->codes)]);
                    }
                    
                    $this->entityManager->persist($impute);
                    $imputeCount++;
                    
                    // Flush tous les 20 imputations
                    if ($imputeCount % 20 === 0) {
                        $this->entityManager->flush();
                    }
                }
            }
        }
        
        $this->entityManager->flush();
        echo $imputeCount . " imputations créées.\n";
    }
}

// Exécuter le script
$kernel = new \App\Kernel('dev', true);
$kernel->boot();

$generator = new TestDataGenerator($kernel);
$generator->execute();
