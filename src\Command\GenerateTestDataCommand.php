<?php

namespace App\Command;

use App\Entity\Code;
use App\Entity\Commentaire;
use App\Entity\Config;
use App\Entity\DMO;
use App\Entity\Document;
use App\Entity\Impute;
use App\Entity\Phase;
use App\Entity\ProductRange;
use App\Entity\Project;
use App\Entity\ReleasedPackage;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

//  php bin/console app:generate-test-data
#[AsCommand(
    name: 'app:generate-test-data',
    description: 'Génère des données de test pour les tables vides',
)]
class GenerateTestDataCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private array $productRanges = [];
    private array $projects = [];
    private array $phases = [];
    private array $codes = [];
    private array $dmos = [];
    private array $users = [];
    private array $documents = [];
    private array $releasedPackages = [];

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Génération des données de test');

        // Récupérer les utilisateurs existants
        $this->users = $this->entityManager->getRepository(User::class)->findAll();
        if (empty($this->users)) {
            $io->error('Aucun utilisateur trouvé dans la base de données. Veuillez d\'abord créer des utilisateurs.');
            return Command::FAILURE;
        }

        // Récupérer les documents existants
        $this->documents = $this->entityManager->getRepository(Document::class)->findAll();

        // Récupérer les packages existants
        $this->releasedPackages = $this->entityManager->getRepository(ReleasedPackage::class)->findAll();
        if (empty($this->releasedPackages)) {
            $io->warning('Aucun package trouvé. Certaines relations ne seront pas établies.');
        }

        // Générer les données dans l'ordre des dépendances
        $this->createProductRanges($io);
        $this->createProjects($io);
        $this->createPhases($io);
        $this->createCodes($io);
        $this->createDMOs($io);
        $this->createCommentaires($io);
        $this->createConfig($io);
        $this->createImputes($io);

        $io->success('Génération des données de test terminée avec succès!');

        return Command::SUCCESS;
    }

    private function createProductRanges(SymfonyStyle $io): void
    {
        $io->section('Création des gammes de produits');

        // Vérifier si des ProductRange existent déjà
        $existingCount = $this->entityManager->getRepository(ProductRange::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount gammes de produits existent déjà. Ajout de nouvelles gammes...");
            $this->productRanges = $this->entityManager->getRepository(ProductRange::class)->findAll();
        }

        $divisions = ['Aerospace', 'Industrial', 'Energy', 'Medical', 'Automotive'];
        $productRangeNames = [
            'Aerospace' => ['Connectors', 'Cables', 'Harnesses', 'Sensors'],
            'Industrial' => ['Automation', 'Robotics', 'Control Systems', 'Power Distribution'],
            'Energy' => ['Solar', 'Wind', 'Hydro', 'Nuclear'],
            'Medical' => ['Imaging', 'Diagnostics', 'Monitoring', 'Therapy'],
            'Automotive' => ['EV Systems', 'Infotainment', 'Safety', 'Powertrain']
        ];

        $newCount = 0;
        foreach ($divisions as $division) {
            foreach ($productRangeNames[$division] as $productRangeName) {
                // Vérifier si cette combinaison existe déjà
                $exists = $this->entityManager->getRepository(ProductRange::class)->findOneBy([
                    'Division' => $division,
                    'ProductRange' => $productRangeName
                ]);

                if (!$exists) {
                    $productRange = new ProductRange();
                    $productRange->setDivision($division);
                    $productRange->setProductRange($productRangeName);

                    $this->entityManager->persist($productRange);
                    $this->productRanges[] = $productRange;
                    $newCount++;
                }
            }
        }

        $this->entityManager->flush();
        $io->text("$newCount nouvelles gammes de produits créées. Total: " . count($this->productRanges));
    }

    private function createProjects(SymfonyStyle $io): void
    {
        $io->section('Création des projets');

        // Vérifier si des Project existent déjà
        $existingCount = $this->entityManager->getRepository(Project::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount projets existent déjà. Ajout de nouveaux projets...");
            $this->projects = $this->entityManager->getRepository(Project::class)->findAll();
        }

        $projectTitles = [
            'Développement connecteur haute performance',
            'Amélioration système de câblage',
            'Nouveau capteur de température',
            'Optimisation processus de fabrication',
            'Réduction des coûts de production',
            'Certification ISO 9001',
            'Expansion marché asiatique',
            'Développement produit médical',
            'Intégration système automobile',
            'Projet R&D énergie renouvelable'
        ];

        $projectManagers = array_filter($this->users, function($user) {
            return in_array('ROLE_PROJECT_MANAGER', $user->getRoles()) || in_array('ROLE_ADMIN', $user->getRoles());
        });

        if (empty($projectManagers)) {
            $projectManagers = $this->users;
        }

        $newCount = 0;
        foreach ($projectTitles as $index => $title) {
            // Vérifier si un projet avec ce titre existe déjà
            $exists = $this->entityManager->getRepository(Project::class)->findOneBy(['Title' => $title]);

            if (!$exists) {
                $project = new Project();
                $project->setTitle($title);
                $project->setOTP('OTP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT));

                if (!empty($projectManagers)) {
                    $project->setProjectManager($projectManagers[array_rand($projectManagers)]);
                }

                $this->entityManager->persist($project);
                $this->projects[] = $project;
                $newCount++;
            }
        }

        $this->entityManager->flush();
        $io->text("$newCount nouveaux projets créés. Total: " . count($this->projects));
    }

    private function createPhases(SymfonyStyle $io): void
    {
        $io->section('Création des phases');

        // Vérifier si des Phase existent déjà
        $existingCount = $this->entityManager->getRepository(Phase::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount phases existent déjà. Ajout de nouvelles phases...");
            $this->phases = $this->entityManager->getRepository(Phase::class)->findAll();
        }

        $phaseTitles = [
            'Conception',
            'Développement',
            'Prototypage',
            'Tests',
            'Validation',
            'Production',
            'Déploiement'
        ];

        $newCount = 0;
        foreach ($this->projects as $project) {
            // Vérifier si ce projet a déjà des phases
            $existingPhases = $this->entityManager->getRepository(Phase::class)->findBy(['projet' => $project]);

            if (empty($existingPhases)) {
                $level = 1;
                foreach ($phaseTitles as $index => $title) {
                    $phase = new Phase();
                    $phase->setTitle($title);
                    $phase->setCode('PH-' . substr($project->getOTP(), -4) . '-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT));
                    $phase->setLevel($level++);
                    $phase->setStatus(rand(0, 1) == 1);
                    $phase->setStatusManuel(rand(0, 1) == 1);
                    $phase->setCommentaire('Commentaire pour la phase ' . $title);
                    $phase->setProjet($project);

                    $this->entityManager->persist($phase);
                    $this->phases[] = $phase;
                    $newCount++;
                }
            } else {
                $this->phases = array_merge($this->phases, $existingPhases);
            }
        }

        $this->entityManager->flush();
        $io->text("$newCount nouvelles phases créées. Total: " . count($this->phases));
    }

    private function createCodes(SymfonyStyle $io): void
    {
        $io->section('Création des codes');

        // Vérifier si des Code existent déjà
        $existingCount = $this->entityManager->getRepository(Code::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount codes existent déjà. Ajout de nouveaux codes...");
            $this->codes = $this->entityManager->getRepository(Code::class)->findAll();
        }

        $codeTitles = [
            'Conception mécanique',
            'Conception électrique',
            'Développement logiciel',
            'Tests fonctionnels',
            'Documentation technique',
            'Gestion de projet',
            'Assurance qualité'
        ];

        $workCenters = ['WC001', 'WC002', 'WC003', 'WC004', 'WC005'];
        $statuses = ['Active', 'Inactive', 'Pending', 'Completed'];

        $newCount = 0;
        foreach ($this->phases as $phase) {
            // Vérifier si cette phase a déjà des codes
            $existingCodes = $this->entityManager->getRepository(Code::class)->findBy(['phase' => $phase]);

            if (empty($existingCodes)) {
                $level = 1;
                foreach ($codeTitles as $index => $title) {
                    $code = new Code();
                    $code->setTitle($title);
                    $code->setCode('CD-' . $phase->getCode() . '-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT));
                    $code->setLevel($level++);
                    $code->setStatus($statuses[array_rand($statuses)]);
                    $code->setWorkCenter($workCenters[array_rand($workCenters)]);
                    $code->setPhase($phase);

                    $this->entityManager->persist($code);
                    $this->codes[] = $code;
                    $newCount++;
                }
            } else {
                $this->codes = array_merge($this->codes, $existingCodes);
            }
        }

        $this->entityManager->flush();
        $io->text("$newCount nouveaux codes créés. Total: " . count($this->codes));
    }

    private function createDMOs(SymfonyStyle $io): void
    {
        $io->section('Création des DMOs');

        // Vérifier si des DMO existent déjà
        $existingCount = $this->entityManager->getRepository(DMO::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount DMOs existent déjà. Ajout de nouveaux DMOs...");
            $this->dmos = $this->entityManager->getRepository(DMO::class)->findAll();
        }

        $descriptions = [
            'Modification de conception pour améliorer la performance',
            'Correction d\'un défaut de fabrication',
            'Optimisation du processus de production',
            'Mise à jour de la documentation technique',
            'Changement de matériau pour réduction des coûts',
            'Adaptation aux nouvelles normes réglementaires',
            'Amélioration de la qualité du produit'
        ];

        $decisions = ['Approved', 'Rejected', 'Pending', 'Under Review'];
        $types = ['Design', 'Process', 'Material', 'Documentation', 'Quality'];

        $engineers = array_filter($this->users, function($user) {
            return in_array('ROLE_ENGINEER', $user->getRoles()) || in_array('ROLE_ADMIN', $user->getRoles());
        });

        if (empty($engineers)) {
            $engineers = $this->users;
        }

        $numToCreate = max(0, 50 - $existingCount);
        $io->progressStart($numToCreate);

        for ($i = 1; $i <= $numToCreate; $i++) {
            $dmo = new DMO();
            $dmo->setDateInit(new \DateTimeImmutable());
            $dmo->setDescription($descriptions[array_rand($descriptions)]);
            $dmo->setDecision($decisions[array_rand($decisions)]);
            $dmo->setStatus(rand(0, 1) == 1);
            $dmo->setEx('EX' . str_pad($existingCount + $i, 4, '0', STR_PAD_LEFT));
            $dmo->setIndusRelated(rand(0, 1) == 1);
            $dmo->setDateEnd(new \DateTime());
            $dmo->setPrNumber(rand(1000, 9999));
            $dmo->setLastUpdateDate(new \DateTime());
            $dmo->setExAssessment('Assessment for DMO #' . ($existingCount + $i));
            $dmo->setSpentTime(rand(1, 100));
            $dmo->setType($types[array_rand($types)]);
            $dmo->setDocument('DOC-' . str_pad($existingCount + $i, 4, '0', STR_PAD_LEFT));

            // Associer à un utilisateur comme demandeur
            if (!empty($this->users)) {
                $dmo->setRequestor($this->users[array_rand($this->users)]);
            }

            // Associer à un ingénieur comme propriétaire
            if (!empty($engineers)) {
                $dmo->setEngOwner($engineers[array_rand($engineers)]);
            }

            // Associer à un utilisateur comme dernier modificateur
            if (!empty($this->users)) {
                $dmo->setLastModificator($this->users[array_rand($this->users)]);
            }

            // Associer à une gamme de produits
            if (!empty($this->productRanges)) {
                $dmo->setProductRange($this->productRanges[array_rand($this->productRanges)]);
            }

            // Associer à un projet
            if (!empty($this->projects)) {
                $dmo->setProjectRelation($this->projects[array_rand($this->projects)]);
            }

            // Associer à des packages
            if (!empty($this->releasedPackages)) {
                $numPackages = rand(1, min(3, count($this->releasedPackages)));
                $selectedPackages = array_rand($this->releasedPackages, $numPackages);
                if (!is_array($selectedPackages)) {
                    $selectedPackages = [$selectedPackages];
                }

                foreach ($selectedPackages as $packageIndex) {
                    $dmo->addReleasedPackage($this->releasedPackages[$packageIndex]);
                }
            }

            $this->entityManager->persist($dmo);
            $this->dmos[] = $dmo;

            // Flush tous les 10 DMOs pour éviter de surcharger la mémoire
            if ($i % 10 === 0) {
                $this->entityManager->flush();
                $io->progressAdvance(10);
            }
        }

        $this->entityManager->flush();
        $io->progressFinish();
        $io->text("$numToCreate nouveaux DMOs créés. Total: " . ($existingCount + $numToCreate));
    }

    private function createCommentaires(SymfonyStyle $io): void
    {
        $io->section('Création des commentaires');

        // Vérifier si des Commentaire existent déjà
        $existingCount = $this->entityManager->getRepository(Commentaire::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount commentaires existent déjà. Ajout de nouveaux commentaires...");
        }

        $commentTexts = [
            'Excellente proposition, je suis d\'accord avec les modifications.',
            'Il faudrait revoir certains aspects techniques avant validation.',
            'Document conforme aux spécifications requises.',
            'Quelques corrections mineures à apporter avant approbation finale.',
            'Validation technique effectuée avec succès.',
            'Des tests supplémentaires sont nécessaires.',
            'Modifications approuvées, prêt pour la prochaine étape.',
            'Révision nécessaire sur les points 3 et 4.',
            'Conforme aux normes ISO en vigueur.',
            'Vérification complète effectuée, aucun problème détecté.'
        ];

        $states = ['draft', 'review', 'approved', 'rejected'];
        $types = ['technical', 'quality', 'process', 'general'];

        $commentCount = 0;
        $totalToCreate = count($this->dmos) * 3 + min(50, count($this->documents)) * 2;
        $io->progressStart($totalToCreate);

        // Commentaires pour les DMOs
        foreach ($this->dmos as $dmo) {
            // Vérifier si ce DMO a déjà des commentaires
            $existingComments = $this->entityManager->getRepository(Commentaire::class)->findBy(['DmoId' => $dmo]);

            if (count($existingComments) < 3) {
                $numComments = rand(1, 3 - count($existingComments));

                for ($i = 0; $i < $numComments; $i++) {
                    $commentaire = new Commentaire();
                    $commentaire->setState($states[array_rand($states)]);
                    $commentaire->setCreatedAt(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));
                    $commentaire->setCommentaire($commentTexts[array_rand($commentTexts)]);
                    $commentaire->setType($types[array_rand($types)]);
                    $commentaire->setDmoId($dmo);

                    if (!empty($this->users)) {
                        $commentaire->setUser($this->users[array_rand($this->users)]);
                    }

                    $this->entityManager->persist($commentaire);
                    $commentCount++;
                    $io->progressAdvance();

                    // Flush tous les 20 commentaires
                    if ($commentCount % 20 === 0) {
                        $this->entityManager->flush();
                    }
                }
            } else {
                $io->progressAdvance(3);
            }
        }

        // Commentaires pour les Documents
        foreach (array_slice($this->documents, 0, min(50, count($this->documents))) as $document) {
            // Vérifier si ce document a déjà des commentaires
            $existingComments = $this->entityManager->getRepository(Commentaire::class)->findBy(['documents' => $document]);

            if (count($existingComments) < 2) {
                $numComments = rand(1, 2 - count($existingComments));

                for ($i = 0; $i < $numComments; $i++) {
                    $commentaire = new Commentaire();
                    $commentaire->setState($states[array_rand($states)]);
                    $commentaire->setCreatedAt(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));
                    $commentaire->setCommentaire($commentTexts[array_rand($commentTexts)]);
                    $commentaire->setType($types[array_rand($types)]);
                    $commentaire->setDocuments($document);

                    if (!empty($this->users)) {
                        $commentaire->setUser($this->users[array_rand($this->users)]);
                    }

                    $this->entityManager->persist($commentaire);
                    $commentCount++;
                    $io->progressAdvance();

                    // Flush tous les 20 commentaires
                    if ($commentCount % 20 === 0) {
                        $this->entityManager->flush();
                    }
                }
            } else {
                $io->progressAdvance(2);
            }
        }

        $this->entityManager->flush();
        $io->progressFinish();
        $io->text("$commentCount nouveaux commentaires créés. Total: " . ($existingCount + $commentCount));
    }

    private function createConfig(SymfonyStyle $io): void
    {
        $io->section('Création des configurations');

        // Vérifier si des Config existent déjà
        $existingCount = $this->entityManager->getRepository(Config::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount configurations existent déjà. Mise à jour des configurations...");
            return;
        }

        // Créer une configuration avec les dates
        $config = new Config();
        $config->setPeriode(new \DateTime('first day of this month'));
        $config->setDateDeb(new \DateTime('first day of this month'));
        $config->setDateFin(new \DateTime('last day of this month'));

        $this->entityManager->persist($config);
        $this->entityManager->flush();

        $io->text("1 nouvelle configuration créée.");
    }

    private function createImputes(SymfonyStyle $io): void
    {
        $io->section('Création des imputations');

        // Vérifier si des Impute existent déjà
        $existingCount = $this->entityManager->getRepository(Impute::class)->count([]);
        if ($existingCount > 0) {
            $io->note("$existingCount imputations existent déjà. Ajout de nouvelles imputations...");
        }

        $users = array_filter($this->users, function($user) {
            return $user->isImputation();
        });

        if (empty($users)) {
            $io->note("Aucun utilisateur avec imputation=true trouvé. Configuration de 10 utilisateurs pour l'imputation...");
            $users = array_slice($this->users, 0, min(10, count($this->users)));
            foreach ($users as $user) {
                $user->setImputation(true);
                $this->entityManager->persist($user);
            }
            $this->entityManager->flush();
        }

        // Créer des imputations pour les 3 derniers mois
        $months = [
            new \DateTime('first day of -2 month'),
            new \DateTime('first day of -1 month'),
            new \DateTime('first day of this month')
        ];

        $imputeCount = 0;
        $totalToCreate = count($users) * count($months) * 10; // Environ 10 imputations par utilisateur par mois
        $io->progressStart($totalToCreate);

        foreach ($users as $user) {
            foreach ($months as $month) {
                // Vérifier si cet utilisateur a déjà des imputations pour ce mois
                $startDate = clone $month;
                $endDate = clone $month;
                $endDate->modify('last day of this month');

                $existingImputes = $this->entityManager->getRepository(Impute::class)->createQueryBuilder('i')
                    ->where('i.user = :user')
                    ->andWhere('i.createdAt >= :start')
                    ->andWhere('i.createdAt <= :end')
                    ->setParameter('user', $user)
                    ->setParameter('start', $startDate)
                    ->setParameter('end', $endDate)
                    ->getQuery()
                    ->getResult();

                if (count($existingImputes) < 10) {
                    // Chaque utilisateur a entre 5 et 15 imputations par mois
                    $numImputes = rand(5, 10 - count($existingImputes));
                    $totalHours = array_reduce($existingImputes, function($carry, $item) {
                        return $carry + $item->getNbHeures();
                    }, 0);
                    $maxHours = 160; // Environ 160 heures par mois

                    for ($i = 0; $i < $numImputes && $totalHours < $maxHours; $i++) {
                        $impute = new Impute();
                        $impute->setUser($user);

                        // Heures restantes ou nombre aléatoire
                        $hours = min(rand(4, 40), $maxHours - $totalHours);
                        $totalHours += $hours;

                        $impute->setNbHeures($hours);

                        // Date dans le mois courant
                        $date = clone $month;
                        $date->modify('+' . rand(0, 27) . ' days');
                        $impute->setCreatedAt($date);

                        // Associer à un code
                        if (!empty($this->codes)) {
                            $impute->setCode($this->codes[array_rand($this->codes)]);
                        }

                        $this->entityManager->persist($impute);
                        $imputeCount++;
                        $io->progressAdvance();

                        // Flush tous les 20 imputations
                        if ($imputeCount % 20 === 0) {
                            $this->entityManager->flush();
                        }
                    }
                } else {
                    $io->progressAdvance(10);
                }
            }
        }

        $this->entityManager->flush();
        $io->progressFinish();
        $io->text("$imputeCount nouvelles imputations créées. Total: " . ($existingCount + $imputeCount));
    }
}
