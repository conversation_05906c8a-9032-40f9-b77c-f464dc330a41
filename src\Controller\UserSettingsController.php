<?php

namespace App\Controller;

use App\Form\UserManagedPlacesType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/user/settings')]
class UserSettingsController extends AbstractController
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    #[Route('/managed-places', name: 'app_user_managed_places')]
    public function managedPlaces(Request $request): Response
    {
        $user = $this->getUser();
        
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $form = $this->createForm(UserManagedPlacesType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Vos places gérées ont été mises à jour avec succès.');
            return $this->redirectToRoute('app_dashboard');
        }

        return $this->render('user/managed_places.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
