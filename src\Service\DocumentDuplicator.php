<?php
namespace App\Service;

use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\Registry;

class DocumentDuplicator
{
    private Registry $registry;
    private $userRepository;

    public function __construct(Registry $registry, \Doctrine\Persistence\ManagerRegistry $doctrine)
    {
        $this->registry = $registry;
        $this->userRepository = $doctrine->getRepository(User::class);

    }

    /**
     * Duplication complète d'un document pour la simulation.
     *
     * Cette méthode :
     * - Copie l’ensemble des propriétés pertinentes et la relation vers ReleasedPackage.
     * - Crée un visa pour chaque place du workflow (nommé "visa_<place>").
     * - Persiste le document cloné.
     * - Simule l'avancement complet du document dans le workflow en appliquant toutes les transitions activables,
     *   tout en mettant à jour l'historique des passages (state_timestamps) et en enregistrant chronologiquement
     *   chaque transition effectuée.
     *
     * @param Document $document Le document original
     * @param EntityManagerInterface $em Pour persister et flusher le document cloné
     * @return array Un tableau associatif avec 'document' (le document dupliqué) et 'history' (le tableau d'historique)
     */
    public function duplicateDocumentWithAllVisasAndSimulation(Document $document, EntityManagerInterface $em): array
    {
        $validator = $this->userRepository->find(1);
        // 1. Création d'une nouvelle instance et copie des propriétés
        $duplicate = new Document();
        
        // --- Copie des propriétés scalaires (ajustez selon vos besoins) ---
        $duplicate->setReference($document->getReference() . '-dup');
        $duplicate->setRefRev($document->getRefRev());
        $duplicate->setRefTitleFra($document->getRefTitleFra());
        $duplicate->setProdDraw($document->getProdDraw());
        $duplicate->setProdDrawRev($document->getProdDrawRev());
        $duplicate->setAlias($document->getAlias());
        $duplicate->setDocType($document->getDocType());
        $duplicate->setMaterialType($document->getMaterialType());
        $duplicate->setProcType($document->getProcType());
        $duplicate->setInventoryImpact($document->getInventoryImpact());
        $duplicate->setIdAletiq($document->getIdAletiq());
        $duplicate->setCustDrawing($document->getCustDrawing());
        $duplicate->setCustDrawingRev($document->getCustDrawingRev());
        $duplicate->setAction($document->getAction());
        $duplicate->setEx($document->getEx());
        $duplicate->setWeight($document->getWeight());
        $duplicate->setWeightUnit($document->getWeightUnit());
        $duplicate->setPlatingSurface($document->getPlatingSurface());
        $duplicate->setPlatingSurfaceUnit($document->getPlatingSurfaceUnit());
        $duplicate->setInternalMachRec($document->isInternalMachRec());
        $duplicate->setCls($document->getCls());
        $duplicate->setMoq($document->getMoq());
        $duplicate->setProductCode($document->getProductCode());
        $duplicate->setProdAgent($document->getProdAgent());
        $duplicate->setMof($document->getMof());
        $duplicate->setCommodityCode($document->getCommodityCode());
        $duplicate->setPurchasingGroup($document->getPurchasingGroup());
        $duplicate->setMatProdType($document->getMatProdType());
        $duplicate->setUnit($document->getUnit());
        $duplicate->setLeadtime($document->getLeadtime());
        $duplicate->setPrisDans1($document->getPrisDans1());
        $duplicate->setPrisDans2($document->getPrisDans2());
        $duplicate->setEccn($document->getEccn());
        $duplicate->setRdo($document->getRdo());
        $duplicate->setHts($document->getHts());
        $duplicate->setFia($document->getFia());
        $duplicate->setMetroTime($document->getMetroTime());
        $duplicate->setQInspection($document->getQInspection());
        $duplicate->setQDynamization($document->getQDynamization());
        $duplicate->setQDocRec($document->getQDocRec());
        $duplicate->setQControlRouting($document->getQControlRouting());
        $duplicate->setCriticalComplete($document->getCriticalComplete());
        $duplicate->setSwitchAletiq($document->isSwitchAletiq());
        $duplicate->setMaterial($document->getMaterial());
        $duplicate->setMetroControl($document->getMetroControl() ?? []);
        $duplicate->setDocImpact($document->isDocImpact());
        $duplicate->setSuperviseur($document->getSuperviseur());
        
        // --- Copie de la relation vers ReleasedPackage ---
        $duplicate->setRelPack($document->getRelPack());
        
        // --- Copie des états actuels et de l'historique ---
        // $duplicate->setCurrentSteps($document->getCurrentSteps());
        // $duplicate->setStateTimestamps($document->getStateTimestamps());
        
        // 2. Création d'un visa pour chaque place définie dans le workflow
        $workflow = $this->registry->get($document, 'document_workflow');
        $workflowDefinition = $workflow->getDefinition();
        // On récupère la liste des places (supposée être un tableau associatif : nom => config)
        $allPlaces = array_keys($workflowDefinition->getPlaces());
        foreach ($allPlaces as $place) {
            $visaName = 'visa_' . $place;
            $dummyVisa = $this->createDummyVisa($visaName, $validator);
            $duplicate->addVisa($dummyVisa);

            $em->persist($dummyVisa);
        }
        
        // 3. Persister le document dupliqué
        $em->persist($duplicate);
        $em->flush();
        
        // 4. Simulation de l’avancement complet dans le workflow
        // La méthode simulateFullWorkflow retourne un tableau d'historique.
        $history = $this->simulateFullWorkflow($duplicate, $em, $workflow);
        
        return [
            'document' => $duplicate,
            'history'  => $history,
        ];
    }
    
    /**
     * Simule l'avancement du document dans le workflow jusqu'à ce qu'il ne soit plus possible d'appliquer de transition.
     * Pour chaque transition appliquée, on enregistre dans un tableau l'état de départ, l'état d'arrivée,
     * le nom de la transition et la date (timestamp).
     *
     * @param Document $document Le document à faire avancer
     * @param EntityManagerInterface $em Pour persister les mises à jour
     * @param mixed $workflow L'instance du workflow associée au document
     * @return array La liste chronologique des passages (chaque entrée contient 'from', 'to', 'transition' et 'timestamp')
     */
    private function simulateFullWorkflow(Document $document, EntityManagerInterface $em, $workflow): array
    {
        $history = [];
        // Récupérer ou initialiser l'historique déjà présent (state_timestamps)
        $stateTimestamps = $document->getStateTimestamps() ?? [];
        
        // Tant qu'il existe des transitions activables
        dd($workflow->getEnabledTransitions($document));
        while (($enabledTransitions = $workflow->getEnabledTransitions($document)) && count($enabledTransitions) > 0) {
            foreach ($enabledTransitions as $transition) {
                $transitionName = $transition->getName();
                // On suppose qu'il y a une unique "from" et au moins une "to"
                $fromPlace = $transition->getFroms()[0] ?? null;
                $toPlaces = $transition->getTos();
                
                foreach ($toPlaces as $toPlace) {
                    // Enregistrez la transition dans l'historique
                    $timestamp = (new \DateTime())->format('Y-m-d H:i:s');
                    $history[] = [
                        'from'       => $fromPlace,
                        'to'         => $toPlace,
                        'transition' => $transitionName,
                        'timestamp'  => $timestamp,
                    ];
                    // Si ce "to" n'a pas encore été enregistré dans state_timestamps, on l'ajoute
                    if (!isset($stateTimestamps[$toPlace])) {
                        $stateTimestamps[$toPlace] = $timestamp;
                    }
                }
                // Appliquer la transition si possible
                if ($workflow->can($document, $transitionName)) {
                    $workflow->apply($document, $transitionName);
                }
            }
            // Mise à jour de l'historique dans l'entité et persistence
            $document->setStateTimestamps($stateTimestamps);
            $em->flush();
        }
        
        return $history;
    }
    
    /**
     * Crée un visa "dummy" avec le nom fourni.
     *
     * @param string $visaName Le nom du visa à créer
     * @param mixed $validator (optionnel) L'utilisateur validateur
     * @return Visa
     */
    private function createDummyVisa(string $visaName, $validator = null): Visa
    {
        $dummyVisa = new Visa();
        $dummyVisa->setName($visaName);
        $dummyVisa->setStatus('valid');
        $dummyVisa->setDateVisa(new \DateTimeImmutable());
        if ($validator) {
            $dummyVisa->setValidator($validator);
        }
        return $dummyVisa;
    }
}
