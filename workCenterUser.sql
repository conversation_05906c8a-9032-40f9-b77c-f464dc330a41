UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSLB002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSQR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSLB002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR004" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSQR002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSQR002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPC001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSLB002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPC001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG003" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT002" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMG001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSQR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDD01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPC001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSFM001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSMT001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSPR001" where email="<EMAIL>";
UPDATE USER SET work_center="ZPSRDE01" where email="<EMAIL>";


SELECT * FROM newbe.user where email in ("<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","","","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>");


INSERT INTO `newbe`.`impute` (`user_id`, `code_id`, `nb_heures`, `created_at`)
VALUES
(2, 91915, 1, '2019-01-01'),
(3, 91922, 2, '2019-01-21'),
(4, 91929, 3, '2019-02-10'),
(5, 91936, 4, '2019-03-02'),
(6, 91943, 5, '2019-03-22'),
(7, 91950, 6, '2019-04-11'),
(8, 91957, 7, '2019-05-01'),
(9, 91964, 8, '2019-05-21'),
(10, 91971, 9, '2019-06-10'),
(11, 91978, 10, '2019-06-30'),
(12, 91985, 1, '2019-07-20'),
(13, 91992, 2, '2019-08-09'),
(14, 91999, 3, '2019-08-29'),
(15, 92006, 4, '2019-09-18'),
(16, 92013, 5, '2019-10-08'),
(17, 92020, 6, '2019-10-28'),
(18, 92027, 7, '2019-11-17'),
(19, 92034, 8, '2019-12-07'),
(20, 92041, 9, '2019-12-27'),
(21, 92048, 10, '2020-01-16'),
(22, 92055, 1, '2020-02-05'),
(23, 92062, 2, '2020-02-25'),
(24, 92069, 3, '2020-03-16'),
(25, 92076, 4, '2020-04-05'),
(26, 92083, 5, '2020-04-25'),
(27, 92090, 6, '2020-05-15'),
(28, 92097, 7, '2020-06-04'),
(29, 92104, 8, '2020-06-24'),
(30, 92111, 9, '2020-07-14'),
(31, 92118, 10, '2020-08-03'),
(32, 92125, 1, '2020-08-23'),
(33, 92132, 2, '2020-09-12'),
(34, 92139, 3, '2020-10-02'),
(35, 92146, 4, '2020-10-22'),
(36, 92153, 5, '2020-11-11'),
(37, 92160, 6, '2020-12-01'),
(38, 92167, 7, '2020-12-21'),
(39, 92174, 8, '2021-01-10'),
(40, 92181, 9, '2021-01-30'),
(41, 92188, 10, '2021-02-19'),
(42, 92195, 1, '2021-03-11'),
(43, 92202, 2, '2021-03-31'),
(44, 92209, 3, '2021-04-20'),
(45, 92216, 4, '2021-05-10'),
(46, 92223, 5, '2021-05-30'),
(47, 92230, 6, '2021-06-19'),
(48, 92237, 7, '2021-07-09'),
(49, 92244, 8, '2021-07-29'),
(50, 92251, 9, '2021-08-18'),
(51, 92258, 10, '2021-09-07'),
(52, 92265, 1, '2021-09-27'),
(53, 92272, 2, '2021-10-17'),
(54, 92279, 3, '2021-11-06'),
(55, 92286, 4, '2021-11-26'),
(56, 92293, 5, '2021-12-16'),
(57, 92300, 6, '2022-01-05'),
(58, 92307, 7, '2022-01-25'),
(59, 92314, 8, '2022-02-14'),
(60, 92321, 9, '2022-03-06'),
(61, 92328, 10, '2022-03-26'),
(62, 92335, 1, '2022-04-15'),
(63, 92342, 2, '2022-05-05'),
(64, 92349, 3, '2022-05-25'),
(65, 92356, 4, '2022-06-14'),
(66, 92363, 5, '2022-07-04'),
(67, 92370, 6, '2022-07-24'),
(68, 92377, 7, '2022-08-13'),
(69, 92384, 8, '2022-09-02'),
(70, 92391, 9, '2022-09-22'),
(71, 92398, 10, '2022-10-12'),
(72, 92405, 1, '2022-10-31'),
(73, 92412, 2, '2022-11-20'),
(74, 92419, 3, '2022-12-10'),
(75, 92426, 4, '2022-12-30'),
(76, 92433, 5, '2023-01-19'),
(77, 92440, 6, '2023-02-08'),
(78, 92447, 7, '2023-02-28'),
(79, 92454, 8, '2023-03-20'),
(80, 92461, 9, '2023-04-09'),
(81, 92468, 10, '2023-04-29'),
(82, 92475, 1, '2023-05-19'),
(83, 92482, 2, '2023-06-08'),
(84, 92489, 3, '2023-06-28'),
(85, 92496, 4, '2023-07-18'),
(86, 92503, 5, '2023-08-07'),
(87, 92510, 6, '2023-08-27'),
(88, 92517, 7, '2023-09-16'),
(89, 92524, 8, '2023-10-06'),
(90, 92531, 9, '2023-10-26'),
(91, 92538, 10, '2023-11-15'),
(92, 92545, 1, '2023-12-05'),
(93, 92552, 2, '2023-12-25'),
(94, 92559, 3, '2024-01-14'),
(95, 92566, 4, '2024-02-03'),
(96, 92573, 5, '2024-02-23'),
(97, 92580, 6, '2024-03-14'),
(98, 92587, 7, '2024-04-03'),
(99, 92594, 8, '2024-04-23'),
(100, 92601, 9, '2024-05-13'),
(101, 92608, 10, '2024-06-02');
