{% extends 'base.html.twig' %}

{% block title %}Suivi des documents{% endblock %}

{% block body %}
<div class="mt-3" style="margin: 0 2%;">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-center mb-0">Suivi des documents</h3>

                {# Légende des couleurs #}
                <div class="d-flex justify-content-center gap-3">
                    <span class="badge rounded-pill panier">
                        Panier
                    </span>
                    <span class="badge rounded-pill old-place">
                        Étape Validée
                    </span>
                    <span class="badge rounded-pill current-step">
                        Étape Actuelle
                    </span>
                    <span class="badge rounded-pill visited-place">
                        Prochaine Étape
                    </span>
                </div>
            </div>

            {# Search and Filter Section #}
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>Recherche et Filtres
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ path('suivie_ref') }}" id="filterForm">
                        <div class="row g-3">
                            {# Global Search #}
                            <div class="col-md-6">
                                <label for="search" class="form-label">Recherche globale</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="{{ filters.search ?? '' }}"
                                           placeholder="Rechercher dans tous les champs...">
                                </div>
                            </div>

                            {# Reference Filter #}
                            <div class="col-md-3">
                                <label for="reference" class="form-label">Référence</label>
                                <input type="text" class="form-control" id="reference" name="reference"
                                       value="{{ filters.reference ?? '' }}"
                                       placeholder="Filtrer par référence...">
                            </div>

                            {# Product Code Filter #}
                            <div class="col-md-3">
                                <label for="productCode" class="form-label">Code Produit</label>
                                <input type="text" class="form-control" id="productCode" name="productCode"
                                       value="{{ filters.productCode ?? '' }}"
                                       placeholder="Filtrer par code produit...">
                            </div>

                            {# Document Type Filter #}
                            <div class="col-md-3">
                                <label for="docType" class="form-label">Type Document</label>
                                <select class="form-select" id="docType" name="docType">
                                    <option value="">Tous les types</option>
                                    {% for docType in filterOptions.docTypes %}
                                        <option value="{{ docType }}" {{ filters.docType == docType ? 'selected' : '' }}>
                                            {{ docType }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            {# Process Type Filter #}
                            <div class="col-md-3">
                                <label for="procType" class="form-label">Type Processus</label>
                                <select class="form-select" id="procType" name="procType">
                                    <option value="">Tous les processus</option>
                                    {% for procType in filterOptions.procTypes %}
                                        <option value="{{ procType }}" {{ filters.procType == procType ? 'selected' : '' }}>
                                            {{ procType }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            {# Activity Filter #}
                            <div class="col-md-3">
                                <label for="activity" class="form-label">Activité</label>
                                <select class="form-select" id="activity" name="activity">
                                    <option value="">Toutes les activités</option>
                                    {% for activity in filterOptions.activities %}
                                        <option value="{{ activity }}" {{ filters.activity == activity ? 'selected' : '' }}>
                                            {{ activity }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            {# Material Filter #}
                            <div class="col-md-3">
                                <label for="material" class="form-label">Matériau</label>
                                <select class="form-select" id="material" name="material">
                                    <option value="">Tous les matériaux</option>
                                    {% for material in filterOptions.materials %}
                                        <option value="{{ material }}" {{ filters.material == material ? 'selected' : '' }}>
                                            {{ material }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            {# Current Step Filter #}
                            <div class="col-md-3">
                                <label for="currentStep" class="form-label">Étape Actuelle</label>
                                <select class="form-select" id="currentStep" name="currentStep">
                                    <option value="">Toutes les étapes</option>
                                    {% for step in filterOptions.currentSteps %}
                                        <option value="{{ step }}" {{ filters.currentStep == step ? 'selected' : '' }}>
                                            {{ step|replace({'_': ' '})|title }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Rechercher
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="clearFilters">
                                        <i class="fas fa-times me-1"></i>Effacer les filtres
                                    </button>
                                    <button type="button" class="btn btn-success" id="exportCsv">
                                        <i class="fas fa-download me-1"></i>Exporter CSV
                                    </button>
                                    <div class="ms-auto">
                                        <span class="badge bg-info fs-6">
                                            Total: {{ pagination.getTotalItemCount }} documents
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {# Table responsive avec amélioration de style #}
            <div class="table-responsive">
                <table class="table table-hover align-middle shadow-sm">
                    <thead id="table-head">
                        <tr>
                            <th scope="col" class="text-center">Pack</th>
                            <th scope="col" class="text-center">Activity</th>
                            <th scope="col" class="text-center" colspan="2">Reference</th>
                            <th scope="col" class="text-center" colspan="2">Prod Drawing</th>
                            <th scope="col" class="text-center">Type</th>
                            <th scope="col" class="text-center" colspan="2">Commentaires</th>
                            <th scope="col">Cheminement</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for result in results %}
                        <tr>
                            <td class="text-center"><a href="{{ path('detail_package', {'id': result.document.relPack.id}) }}" class="badge bg-primary pack-link">{{result.document.relPack.id}}</a></td>
                            <td class="text-center">{{ result.document.relPack.activity }}</td>
                            <td class="text-center">{{ result.document.reference }}</td>
                            <td class="text-center">{{ result.document.refRev }}</td>
                            <td class="text-center">{{ result.document.prodDraw }}</td>
                            <td class="text-center">{{ result.document.prodDrawRev }}</td>
                            <td class="text-center type">
                                <p class="mb-0">{{ result.document.docType }}</p>
                                <p class="mb-0">{{ result.document.procType }}</p>
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.PrincipalCommentaires|length > 0 %}
                                    <div class="tooltip-container"  data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.PrincipalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-user"></i></span>
                                    </div>
                                {% endif %}
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.GlobalCommentaires|length > 0 %}
                                    <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.GlobalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-users"></i></span>
                                    </div>
                                {% endif %}
                            </td>

                            <td class="gap-3 p-3" style="white-space: wrap;">
                                {% for place, val in result.oldPlaces %}
                                    <a
                                        class="badge rounded-pill old-place"
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.oldPlaces %}
                                    <a
                                        class="badge rounded-pill old-place panier"
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}?document-id={{ result.document.id }}#document-id={{ result.document.id }}"
                                       class="badge rounded-pill current-step"
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}#onlget={{ place }}&document-id={{ result.document.id }}"
                                       class="badge rounded-pill current-step panier"
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.visitedPlaces %}
                                    <a
                                        class="badge rounded-pill visited-place"
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.paniers.visitedPlaces %}
                                    <a
                                        class="badge rounded-pill visited-place panier"
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>

            {# Pagination améliorée #}
            <div>
                {{ knp_pagination_render(pagination) }}
            </div>

        </div>
    </div>
</div>

<style>
/* Styles pour les badges */
.old-place {
    background-color: #28a745;
    color: white;
}

.current-step {
    background-color:rgb(0, 17, 255);
    color: white;
}

.visited-place {
    background-color:rgb(73, 161, 255);
    color: white;
}

table thead th {
    font-weight: bold;
}

* {
    user-select: none;
}

td, span {
    font-size: 0.90rem;
    white-space: nowrap;
}

.type{
    font-size: 0.75rem;
}

th {
    font-size: 0.95rem;
    white-space: nowrap;
    background-color: #004080!important;
    color: #fff!important;

}
.old-place.panier {
    background: repeating-linear-gradient(
    -55deg,
    #28a745,
    #28a745 10px,
    rgba(40, 167, 69, 0.65) 10px,
    rgba(40, 167, 69, 0.65) 20px
    );
}

.current-step.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(0, 17, 255),
    rgb(0, 17, 255) 10px,
    rgb(0, 17, 255, 0.65) 10px,
    rgb(0, 17, 255, 0.65) 20px
    );
}

.visited-place.panier {
    background: repeating-linear-gradient(
    -55deg,
        rgb(73, 161, 255),
        rgb(73, 161, 255) 10px,
        rgba(73, 161, 255, 0.65) 10px,
        rgb(73, 161, 255, 0.65) 20px
    );
}

.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(95, 95, 95),
    rgb(95, 95, 95) 10px,
    rgba(44, 44, 44, 0.65) 10px,
    rgba(44, 44, 44, 0.65) 20px
);
}

/* Enhanced styling for search and filter section */
.card-header.bg-primary {
    background: linear-gradient(135deg, #0059B3 0%, #004080 100%) !important;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0059B3;
    box-shadow: 0 0 0 0.2rem rgba(0, 89, 179, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #0059B3 0%, #004080 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #004080 0%, #003366 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 89, 179, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    transform: translateY(-1px);
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #004080 0%, #0059B3 100%) !important;
    border: none !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 89, 179, 0.1) !important;
    transform: scale(1.01);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    margin: 0.1rem;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.05);
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #ced4da;
}

/* Search highlighting */
mark {
    background-color: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
    font-weight: bold;
}

/* Loading states */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .d-flex.gap-2.flex-wrap {
        flex-direction: column;
    }

    .d-flex.gap-2.flex-wrap .btn {
        margin-bottom: 0.5rem;
    }

    .ms-auto {
        margin-left: 0 !important;
        margin-top: 1rem;
    }
}

/* Animation for filter cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

</style>
<script>
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
        customClass: 'custom-tooltip'
    }))

    $(document).ready(function() {
        // Real-time search functionality
        let searchTimeout;

        // Auto-submit form on input change with debounce
        $('#search, #reference, #productCode').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                $('#filterForm').submit();
            }, 500); // 500ms delay
        });

        // Auto-submit form on select change
        $('#docType, #procType, #activity, #material, #currentStep').on('change', function() {
            $('#filterForm').submit();
        });

        // Clear filters functionality
        $('#clearFilters').on('click', function() {
            // Clear all form inputs
            $('#filterForm')[0].reset();

            // Remove URL parameters and reload
            window.location.href = '{{ path('suivie_ref') }}';
        });

        // Export CSV functionality
        $('#exportCsv').on('click', function() {
            // Get current filter values
            const formData = $('#filterForm').serialize();

            // Create export URL with current filters
            const exportUrl = '{{ path('suivie_ref_export') }}?' + formData;

            // Show loading indicator
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Génération...');

            // Create temporary link and trigger download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = 'suivi_documents_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.csv';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Reset button after a short delay
            setTimeout(() => {
                $(this).prop('disabled', false).html('<i class="fas fa-download me-1"></i>Exporter CSV');
            }, 2000);
        });

        // Enhanced table filtering for immediate visual feedback
        function filterTableRows() {
            const searchTerm = $('#search').val().toLowerCase();
            const referenceTerm = $('#reference').val().toLowerCase();
            const productCodeTerm = $('#productCode').val().toLowerCase();
            const docType = $('#docType').val();
            const procType = $('#procType').val();
            const activity = $('#activity').val();
            const material = $('#material').val();
            const currentStep = $('#currentStep').val();

            $('tbody tr').each(function() {
                const row = $(this);
                let showRow = true;

                // Check global search
                if (searchTerm && !row.text().toLowerCase().includes(searchTerm)) {
                    showRow = false;
                }

                // Check specific filters
                if (referenceTerm && !row.find('td:nth-child(3)').text().toLowerCase().includes(referenceTerm)) {
                    showRow = false;
                }

                if (productCodeTerm && !row.text().toLowerCase().includes(productCodeTerm)) {
                    showRow = false;
                }

                if (docType && !row.find('td:nth-child(7)').text().includes(docType)) {
                    showRow = false;
                }

                if (procType && !row.find('td:nth-child(7)').text().includes(procType)) {
                    showRow = false;
                }

                if (activity && !row.find('td:nth-child(2)').text().includes(activity)) {
                    showRow = false;
                }

                // Check current step in workflow badges (last column)
                if (currentStep && !row.find('td:last-child').text().toLowerCase().includes(currentStep.toLowerCase())) {
                    showRow = false;
                }

                row.toggle(showRow);
            });

            // Update visible count
            updateVisibleCount();
        }

        function updateVisibleCount() {
            const visibleRows = $('tbody tr:visible').length;
            const totalRows = $('tbody tr').length;

            // Update the count badge if it exists
            $('.badge.bg-info').text(`Affichés: ${visibleRows} / Total: {{ pagination.getTotalItemCount }}`);
        }

        // Apply client-side filtering on input for immediate feedback
        $('#search, #reference, #productCode, #docType, #procType, #activity, #material, #currentStep').on('input change', function() {
            filterTableRows();
        });

        // Highlight search terms in table
        function highlightSearchTerms() {
            const searchTerm = $('#search').val();
            if (searchTerm.length > 2) {
                $('tbody td').each(function() {
                    const cell = $(this);
                    const text = cell.text();
                    const highlightedText = text.replace(
                        new RegExp('(' + searchTerm + ')', 'gi'),
                        '<mark>$1</mark>'
                    );
                    if (text !== highlightedText) {
                        cell.html(highlightedText);
                    }
                });
            }
        }

        // Apply highlighting after search
        $('#search').on('input', function() {
            setTimeout(highlightSearchTerms, 100);
        });

        // Show loading indicator during form submission
        $('#filterForm').on('submit', function() {
            showGlobalLoading();
        });
    });
</script>

<style>
.custom-tooltip .tooltip-inner {
    white-space: normal;
    max-width: none;
}
</style>

{% endblock %}
