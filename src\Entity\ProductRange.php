<?php

namespace App\Entity;

use App\Repository\ProductRangeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProductRangeRepository::class)]
class ProductRange
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $ProductRange = null;

    #[ORM\Column(length: 255)]
    private ?string $Division = null;

    #[ORM\Column]
    private ?bool $etat = null;

    #[ORM\OneToMany(targetEntity: DMO::class, mappedBy: 'productRange', orphanRemoval: true)]
    private Collection $DMO;

    public function __construct()
    {
        $this->DMO = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProductRange(): ?string
    {
        return $this->ProductRange;
    }

    public function setProductRange(string $ProductRange): static
    {
        $this->ProductRange = $ProductRange;
        return $this;
    }

    public function getDivision(): ?string
    {
        return $this->Division;
    }

    public function setDivision(string $Division): static
    {
        $this->Division = $Division;
        return $this;
    }

    public function isEtat(): ?bool
    {
        return $this->etat;
    }

    public function setEtat(bool $etat): static
    {
        $this->etat = $etat;
        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getDMO(): Collection
    {
        return $this->DMO;
    }

    public function addDMO(DMO $dMO): static
    {
        if (!$this->DMO->contains($dMO)) {
            $this->DMO->add($dMO);
            $dMO->setProductRange($this);
        }

        return $this;
    }

    public function removeDMO(DMO $dMO): static
    {
        if ($this->DMO->removeElement($dMO)) {
            // set the owning side to null (unless already changed)
            if ($dMO->getProductRange() === $this) {
                $dMO->setProductRange(null);
            }
        }

        return $this;
    }
}
