{% extends 'base.html.twig' %}

{% block title %}Personnaliser le tableau de bord{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .dashboard-header {
        background-color: #f0f4f8;
        border-radius: 4px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #0275d8;
    }

    .widget-card {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.2s ease;
        cursor: pointer;
        border: 1px solid #e9ecef;
        height: 100%;
    }

    .widget-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .widget-card.active {
        border: 2px solid #0275d8;
        box-shadow: 0 4px 8px rgba(2, 117, 216, 0.2);
    }

    .widget-card .card-body {
        padding: 20px;
        text-align: center;
    }

    .widget-icon {
        font-size: 2rem;
        color: #0275d8;
        margin-bottom: 15px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .widget-title {
        font-weight: 600;
        margin-bottom: 10px;
        color: #212529;
        font-size: 1.1rem;
    }

    .widget-description {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .widget-toggle {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }

    .form-check-input {
        width: 1.25em;
        height: 1.25em;
        cursor: pointer;
    }

    .form-check-input:checked {
        background-color: #0275d8;
        border-color: #0275d8;
    }

    .btn-primary, .btn-outline-secondary {
        border-radius: 4px;
        font-weight: 500;
        padding: 0.5rem 1rem;
    }

    .btn-primary {
        background-color: #0275d8;
        border-color: #0275d8;
    }

    .btn-primary:hover {
        background-color: #0267bf;
        border-color: #0267bf;
    }
</style>
{% endblock %}

{% block body %}
<div class="container-fluid py-4 px-4 bg-light">
    <!-- En-tête de la page de personnalisation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-welcome p-4 rounded bg-white shadow-sm border-start border-primary border-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 fw-bold text-dark mb-1">
                            <i class="fas fa-cog text-primary me-2"></i>
                            Personnaliser le tableau de bord
                        </h1>
                        <p class="text-muted mb-0">Sélectionnez les widgets que vous souhaitez afficher sur votre tableau de bord</p>
                    </div>
                    <div>
                        <button id="savePreferences" class="btn btn-primary me-2">
                            <i class="fas fa-save me-1"></i> Enregistrer
                        </button>
                        <a href="{{ path('app_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info bg-white border-start border-info border-4 shadow-sm">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-info-circle text-info fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading fw-bold">Comment personnaliser votre tableau de bord</h5>
                        <p class="mb-0">Cliquez sur les cartes ci-dessous pour activer ou désactiver les widgets. Les widgets activés apparaîtront sur votre tableau de bord. N'oubliez pas d'enregistrer vos modifications.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widgets disponibles -->
    <div class="row mb-4">
        <div class="col-12 mb-3">
            <h4 class="fw-bold text-dark">
                <i class="fas fa-th-large text-primary me-2"></i>
                Widgets disponibles
            </h4>
        </div>

        {% for key, widget in available_widgets %}
            <div class="col-md-4 col-lg-3 mb-4">
                <div class="card border-0 shadow-sm h-100 widget-card position-relative {% if widgets[key] is defined and widgets[key] %}active{% endif %}" data-widget-key="{{ key }}">
                    <div class="card-body p-4 text-center d-flex flex-column">
                        <div class="widget-toggle form-check form-switch position-absolute top-0 end-0 m-3">
                            <input class="form-check-input" type="checkbox" id="widget-{{ key }}" {% if widgets[key] is defined and widgets[key] %}checked{% endif %}>
                        </div>

                        <div class="widget-icon mb-3 mx-auto">
                            <div class="icon-circle bg-primary bg-opacity-10 p-3 rounded-circle">
                                <i class="fas {{ widget.icon }} fa-2x text-primary"></i>
                            </div>
                        </div>

                        <h5 class="widget-title mb-2">{{ widget.title }}</h5>
                        <p class="widget-description text-muted mb-0 flex-grow-1">{{ widget.description }}</p>

                        <div class="mt-3 pt-3 border-top">
                            <span class="badge {% if widgets[key] is defined and widgets[key] %}bg-success{% else %}bg-secondary{% endif %} rounded-pill px-3 py-2">
                                {% if widgets[key] is defined and widgets[key] %}
                                    <i class="fas fa-check-circle me-1"></i> Activé
                                {% else %}
                                    <i class="fas fa-times-circle me-1"></i> Désactivé
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-12 text-center">
            <button id="savePreferences" class="btn btn-lg btn-primary me-2 px-4 py-2">
                <i class="fas fa-save me-2"></i> Enregistrer les modifications
            </button>
            <a href="{{ path('app_dashboard') }}" class="btn btn-lg btn-outline-secondary px-4 py-2">
                <i class="fas fa-arrow-left me-2"></i> Retour au tableau de bord
            </a>
        </div>
    </div>
</div>

<style>
    .bg-light {
        background-color: #f8f9fa !important;
    }

    .icon-circle {
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .widget-card {
        cursor: pointer;
    }

    .widget-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .widget-card.active {
        border-top: 4px solid #0275d8 !important;
    }

    .form-check-input {
        width: 2.5em;
        height: 1.25em;
        cursor: pointer;
    }

    .form-check-input:checked {
        background-color: #0275d8;
        border-color: #0275d8;
    }

    .btn-primary {
        background-color: #0275d8;
        border-color: #0275d8;
    }

    .btn-primary:hover {
        background-color: #0267bf;
        border-color: #0267bf;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gérer le clic sur les cartes de widget
        const widgetCards = document.querySelectorAll('.widget-card');
        widgetCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Ne pas déclencher si on clique sur le switch directement
                if (e.target.type === 'checkbox' || e.target.closest('.form-check-input')) {
                    return;
                }

                const checkbox = this.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                updateCardState(this, checkbox.checked);
            });
        });

        // Gérer le clic sur les checkboxes sans propager l'événement
        const checkboxes = document.querySelectorAll('.widget-toggle input');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                const card = this.closest('.widget-card');
                updateCardState(card, this.checked);
            });
        });

        // Fonction pour mettre à jour l'état visuel de la carte
        function updateCardState(card, isActive) {
            card.classList.toggle('active', isActive);

            // Mettre à jour le badge
            const badge = card.querySelector('.badge');
            if (badge) {
                if (isActive) {
                    badge.classList.remove('bg-secondary');
                    badge.classList.add('bg-success');
                    badge.innerHTML = '<i class="fas fa-check-circle me-1"></i> Activé';
                } else {
                    badge.classList.remove('bg-success');
                    badge.classList.add('bg-secondary');
                    badge.innerHTML = '<i class="fas fa-times-circle me-1"></i> Désactivé';
                }
            }
        }

        // Gérer l'enregistrement des préférences (pour les deux boutons)
        const saveButtons = document.querySelectorAll('#savePreferences');
        saveButtons.forEach(button => {
            button.addEventListener('click', savePreferences);
        });

        function savePreferences() {
            // Afficher un indicateur de chargement
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Enregistrement...';
            this.disabled = true;

            const widgets = {};

            // Collecter l'état de tous les widgets
            widgetCards.forEach(card => {
                const key = card.dataset.widgetKey;
                const checkbox = card.querySelector('input[type="checkbox"]');
                widgets[key] = checkbox.checked;
            });

            // Envoyer les préférences au serveur
            fetch('{{ path('app_dashboard_save_preferences') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ widgets: widgets }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Afficher un message de succès avant la redirection
                    showToast('Succès', 'Vos préférences ont été enregistrées avec succès.', 'success');

                    // Rediriger après un court délai
                    setTimeout(() => {
                        window.location.href = '{{ path('app_dashboard') }}';
                    }, 1500);
                } else {
                    // Réinitialiser le bouton
                    saveButtons.forEach(btn => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    });

                    showToast('Erreur', 'Erreur lors de l\'enregistrement des préférences: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);

                // Réinitialiser le bouton
                saveButtons.forEach(btn => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });

                showToast('Erreur', 'Une erreur est survenue lors de l\'enregistrement des préférences.', 'error');
            });
        }

        // Fonction pour afficher un toast de notification
        function showToast(title, message, type) {
            // Créer le toast s'il n'existe pas déjà
            if (!document.getElementById('customToast')) {
                const toastHTML = `
                    <div id="customToast" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
                        <div class="toast hide" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
                            <div class="toast-header">
                                <span class="toast-icon me-2"></span>
                                <strong class="me-auto toast-title"></strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                            <div class="toast-body"></div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', toastHTML);
            }

            // Configurer le toast
            const toastEl = document.querySelector('#customToast .toast');
            const toastTitle = toastEl.querySelector('.toast-title');
            const toastBody = toastEl.querySelector('.toast-body');
            const toastIcon = toastEl.querySelector('.toast-icon');

            // Définir le contenu
            toastTitle.textContent = title;
            toastBody.textContent = message;

            // Définir l'icône en fonction du type
            if (type === 'success') {
                toastIcon.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            } else if (type === 'error') {
                toastIcon.innerHTML = '<i class="fas fa-exclamation-circle text-danger"></i>';
            } else {
                toastIcon.innerHTML = '<i class="fas fa-info-circle text-info"></i>';
            }

            // Afficher le toast
            const toast = new bootstrap.Toast(toastEl);
            toast.show();
        }
    });
</script>
{% endblock %}
