CREATE TABLE users (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL UNIQUE,
    role VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE released_packages (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    Rel_Pack_Num INT NOT NULL UNIQUE,
    Rel_Pack_Owner VARCHAR(100) NOT NULL,
    Creation_Date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Description TEXT
);
CREATE TABLE documents (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    Reference VARCHAR(25) NOT NULL,
    Ref_Rev VARCHAR(3) NOT NULL,
    Ref_Title_FRA VARCHAR(45) NOT NULL,
    Prod_Draw VARCHAR(45) NOT NULL,
    Prod_Draw_Rev VARCHAR(3) NOT NULL,
    Drawing_Path VARCHAR(150) NOT NULL,
    <PERSON><PERSON> VARCHAR(45) NOT NULL,
    Doc_Type VARCHAR(45) NOT NULL,
    Rel_Pack_ID INT NOT NULL,
    FOREIGN KEY (Rel_Pack_ID) REFERENCES released_packages(ID)
);
CREATE TABLE document_status_history (
    history_id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    action VARCHAR(255) NOT NULL,
    performed_by INT NOT NULL,
    action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comments TEXT,
    FOREIGN KEY (document_id) REFERENCES documents(ID),
    FOREIGN KEY (performed_by) REFERENCES users(ID)
);
CREATE TABLE visas (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    ID_Released_Drawing INT NOT NULL,
    validator_id INT NOT NULL,
    status VARCHAR(255) NOT NULL,
    date_visa TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ID_Released_Drawing) REFERENCES documents(ID),
    FOREIGN KEY (validator_id) REFERENCES users(ID)
);
