<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Entity\ReleasedPackage;
use App\Entity\Project;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:migrate-legacy',
    description: 'Migre les données de l’ancienne BD vers le nouveau schéma'
)]
class MigrateLegacyDataCommand extends Command
{
    protected static $defaultName = 'app:migrate-legacy';

    // Cache pour éviter les recherches répétées d'utilisateurs
    private array $userCache = [];
    private ?User $adminUser = null;

    public function __construct(
        #[ConnectionName('legacy')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private UserRepository $users,
        private ProjectRepository $projects
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'Limiter le nombre d’enregistrements à migrer');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $input->getOption('limit');

        // Optimisations mémoire pour la migration
        $this->optimizeForMigration($output);

        // Mapping pour associer les anciens IDs aux nouveaux packages
        $packageMapping = [];

        // 0. Nettoyer les tables existantes pour éviter les doublons
        $this->cleanExistingData($output);

        // 0.5. Initialiser le cache des utilisateurs
        $this->initializeUserCache($output);

        // 1. Créer les projets manquants
        $this->createMissingProjects($output);

        // 3. Migrer TOUS les ReleasedPackage d'abord (pas de limite pour éviter les références manquantes)
        $sql = 'SELECT * FROM tbl_released_package';
        $rows = $this->oldDb->fetchAllAssociative($sql);

        $packageMapping = []; // Mapping ancien ID -> nouveau package
        $packageVisaData = []; // Stocker les données de visa des packages
        $packageCount = 0;
        foreach ($rows as $r) {
            // on n’a plus le format prénom.nom, on garde juste le nom (avant l’espace)
            // Recherche flexible des utilisateurs avec cache
            $owner = $this->findUserWithCache($r['Rel_Pack_Owner'], $output);
            $verif = $this->findUserWithCache($r['Verif_Req_Owner'], $output);
            $valid = $this->findUserWithCache($r['BE_3_Req_Owner'], $output);

            // Seul le owner est obligatoire, verif et valid peuvent être optionnels
            if (!$owner) {
                $output->writeln(sprintf(
                    '<error>Utilisateur introuvable pour package %s : owner=%s, verif=%s, valid=%s</error>',
                    $r['Rel_Pack_Num'], $r['Rel_Pack_Owner'], $r['Verif_Req_Owner'], $r['BE_3_Req_Owner']
                ));
                continue;
            }

            // Log des utilisateurs manquants mais non bloquants
            if (!$verif && !empty($r['Verif_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Verif introuvable pour package %s : verif=%s</comment>',
                    $r['Rel_Pack_Num'], $r['Verif_Req_Owner']
                ));
            }
            if (!$valid && !empty($r['BE_3_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Valid introuvable pour package %s : valid=%s</comment>',
                    $r['Rel_Pack_Num'], $r['BE_3_Req_Owner']
                ));
            }

            $proj = $this->projects->findOneByOtp($r['Project']);
            if (!$proj) {
                $output->writeln(sprintf(
                    '<error>Project OTP introuvable : %s (package %s)</error>',
                    $r['Project'], $r['Rel_Pack_Num']
                ));
                continue;
            }

            // Créer l'entité ReleasedPackage avec l'ORM
            $package = new ReleasedPackage();
            $package->setOwner($owner);
            $package->setVerif($verif);
            $package->setValid($valid);
            $package->setProjectRelation($proj);
            $package->setDescription($r['Observations']);
            $package->setActivity($r['Activity']);
            $package->setEx($r['Ex']);

            // Conversion des dates si elles ne sont pas nulles et valides
            if (!empty($r['Reservation_Date']) && $this->isValidDate($r['Reservation_Date'])) {
                $package->setReservationDate(new \DateTime($r['Reservation_Date']));
            }
            if (!empty($r['Creation_Date']) && $this->isValidDate($r['Creation_Date'])) {
                $package->setCreationDate(new \DateTime($r['Creation_Date']));
            }

            $this->em->persist($package);

            // Stocker les données de visa du package pour les appliquer aux documents plus tard
            $packageVisaData[$r['Rel_Pack_Num']] = $r;
            $packageCount++;

            // Flush périodique pour éviter les problèmes de mémoire
            if ($packageCount % 100 === 0) {
                $this->em->flush();

                // Stocker l'ID après flush (quand l'ID est généré)
                $packageMapping[$r['Rel_Pack_Num']] = $package->getId();

                $this->em->clear(); // Libérer la mémoire
                $output->writeln("<comment>Flush packages: $packageCount traités</comment>");

                // Recharger l'utilisateur Admin et vider le cache après clear()
                $this->reloadAfterClear();
            } else {
                // Stocker l'ID immédiatement si pas de flush
                $packageMapping[$r['Rel_Pack_Num']] = $package;
            }
        }

        // Flush des packages avant de migrer les documents
        $this->em->flush();

        // Finaliser le mapping : convertir toutes les entités en IDs
        foreach ($packageMapping as $oldId => $packageOrId) {
            if (is_object($packageOrId)) {
                $packageMapping[$oldId] = $packageOrId->getId();
            }
        }

        $output->writeln('<info>Packages migrés avec succès.</info>');

        // 4. Migrer les Documents via ORM (appliquer la limite ici)
        $sql = 'SELECT * FROM tbl_released_drawing' . ($limit ? ' LIMIT ' . (int)$limit : '');
        $draws = $this->oldDb->fetchAllAssociative($sql);

        $documentCount = 0;
        foreach ($draws as $d) {
            // Vérifier que le package existe dans notre mapping
            if (!isset($packageMapping[$d['Rel_Pack_Num']])) {
                $output->writeln(sprintf(
                    '<e>Package introuvable pour document %s (Rel_Pack_Num: %s)</e>',
                    $d['Reference'], $d['Rel_Pack_Num']
                ));
                continue;
            }

            $doc = new Document();
            // Récupérer le package par son ID depuis le mapping
            $packageId = $packageMapping[$d['Rel_Pack_Num']];
            $package = $this->em->getReference(ReleasedPackage::class, $packageId);
            $doc->setRelPack($package);

            // Fonction helper pour setter les valeurs seulement si elles existent
            $setSafe = function($setter, $key, $transform = null) use ($doc, $d) {
                if (isset($d[$key])) {
                    $value = $transform ? $transform($d[$key]) : $d[$key];
                    $doc->$setter($value);
                }
            };

            $setSafe('setReference', 'Reference');
            $setSafe('setRefRev', 'Ref_Rev');
            $setSafe('setRefTitleFra', 'Ref_Title');
            $setSafe('setProdDraw', 'Prod_Draw');
            $setSafe('setProdDrawRev', 'Prod_Draw_Rev');
            $setSafe('setAlias', 'Alias');
            $setSafe('setDocType', 'Doc_Type');
            $setSafe('setMaterialType', 'Material_Type');
            $setSafe('setProcType', 'Proc_Type');
            $setSafe('setInventoryImpact', 'Inventory_Impact');
            $setSafe('setCustDrawing', 'Cust_Drawing');
            $setSafe('setCustDrawingRev', 'Cust_Drawing_Rev');
            $setSafe('setAction', 'Action');
            $setSafe('setEx', 'Ex');
            $setSafe('setWeight', 'Weight');
            $setSafe('setWeightUnit', 'Weight_Unit');
            $setSafe('setPlatingSurface', 'Plating_Surface');
            $setSafe('setPlatingSurfaceUnit', 'Plating_Surface_Unit');
            $setSafe('setInternalMachRec', 'Internal_Mach_Rec', fn($v) => (bool)$v);
            $setSafe('setCls', 'CLS');
            $setSafe('setMoq', 'MOQ');
            $setSafe('setProductCode', 'Product_Code');
            $setSafe('setProdAgent', 'Prod_Agent');
            $setSafe('setMof', 'MOF');
            $setSafe('setCommodityCode', 'Commodity_Code');
            $setSafe('setPurchasingGroup', 'Purchasing_Group');
            $setSafe('setMatProdType', 'Mat_Prod_Type');
            $setSafe('setUnit', 'Unit');
            $setSafe('setLeadtime', 'leadtime');
            $setSafe('setPrisDans1', 'Pris_Dans1');
            $setSafe('setPrisDans2', 'Pris_Dans2');
            $setSafe('setEccn', 'ECCN');
            $setSafe('setRdo', 'RDO');
            $setSafe('setHts', 'HTS');
            $setSafe('setFia', 'FIA');
            $setSafe('setMetroTime', 'Metro_Time');
            // MetroControl doit être un array, pas une string
            if (!empty($d['Metro_Control'])) {
                // Si c'est une string, on la convertit en array
                $metroControlArray = is_string($d['Metro_Control']) ? [$d['Metro_Control']] : $d['Metro_Control'];
                $doc->setMetroControl($metroControlArray);
            }
            // QInspection doit être un array
            if (!empty($d['Q_Inspection'])) {
                $qInspectionArray = is_string($d['Q_Inspection']) ? [$d['Q_Inspection']] : $d['Q_Inspection'];
                $doc->setQInspection($qInspectionArray);
            }
            $setSafe('setQDynamization', 'Q_Dynamization');
            // QDocRec doit être un array
            if (isset($d['Q_Doc_Req']) && !empty($d['Q_Doc_Req'])) {
                $qDocRecArray = is_string($d['Q_Doc_Req']) ? [$d['Q_Doc_Req']] : $d['Q_Doc_Req'];
                $doc->setQDocRec($qDocRecArray);
            }
            $setSafe('setQControlRouting', 'Q_Control_Routing');
            $setSafe('setCriticalComplete', 'Critical_Complete');
            $setSafe('setSwitchAletiq', 'SWITCH_ALETIQ', fn($v) => (bool)$v);
            $setSafe('setIdAletiq', 'ID_ALETIQ');
            $setSafe('setMaterial', 'Material');
            // DocImpact est obligatoire, valeur par défaut 0 si manquant
            $docImpact = isset($d['Doc_Impact']) ? (int)$d['Doc_Impact'] : 0;
            $doc->setDocImpact($docImpact);

            // Déterminer l'état actuel du document basé sur les visas
            $this->setDocumentWorkflowState($doc, $d, $output);

            $this->em->persist($doc);

            // Commentaires
            if (!empty($d['Requestor_Comments'])) {
                $c = new Commentaire();
                $c->setUser($doc->getSuperviseur());
                $c->setDocuments($doc);
                $c->setType('request');
                $c->setCommentaire($d['Requestor_Comments']);
                $c->setCreatedAt(new \DateTimeImmutable()); // Champ obligatoire
                $this->em->persist($c);
            }

            // Visas - Mapping correct des noms de visas (basé sur VisaChecker.php)
            $visaMapping = [
                'Project' => 'visa_Project',
                'Inventory' => 'visa_Logistique', // Legacy Inventory maps to Logistique
                'Product' => 'visa_produit',
                'Quality' => 'visa_Quality',
                'Method' => 'visa_Methode_assemblage',
                'Finance' => 'visa_Costing', // Legacy Finance maps to Costing
                'Prod' => 'visa_prod',
                'Supply' => 'visa_Logistique', // Legacy Supply maps to Logistique
                'PUR_1' => 'visa_Achat_Rfq', // Legacy PUR_1 maps to Achat_Rfq
                'Metro' => 'visa_Metro',
                'Planning' => 'visa_Planning',
                'GID' => 'visa_GID',
                'GID_2' => 'visa_Core_Data', // Legacy GID_2 maps to Core_Data
                'ROUTING_ENTRY' => 'visa_Prod_Data', // Legacy ROUTING_ENTRY maps to Prod_Data
                'hts' => 'visa_Achat_Hts',
            ];

            foreach ($visaMapping as $visaKey => $visaName) {
                $visaCol = "VISA_{$visaKey}";
                $dateCol = "DATE_{$visaKey}";
                if (isset($d[$visaCol]) && !empty($d[$visaCol]) && isset($d[$dateCol]) && $this->isValidDate($d[$dateCol])) {
                    $v = new Visa();
                    $v->setName($visaName);
                    $v->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $d[$dateCol]) ?: new \DateTimeImmutable($d[$dateCol]));
                    $v->setStatus('valid');
                    $v->setReleasedDrawing($doc);

                    // Ajouter un validator (utiliser Admin en fallback)
                    if ($this->adminUser) {
                        $v->setValidator($this->adminUser);
                    }

                    $this->em->persist($v);
                }
            }

            // Appliquer les visas du package (si applicable)
            $this->migratePackageVisasForDocument($doc, $d, $output);

            // Flush immédiatement pour que les visas soient disponibles
            $this->em->flush();

            // Rafraîchir l'entité pour charger les visas créés
            $this->em->refresh($doc);

            // Maintenant déterminer l'état actuel du document basé sur les visas créés
            $this->setDocumentWorkflowState($doc, $d, $output);

            $documentCount++;

            // Flush ultra fréquent pour éviter les problèmes de mémoire
            if ($documentCount % 5 === 0) {
                $this->em->flush();
                $this->em->clear(); // Libérer la mémoire
                $output->writeln("<comment>Flush documents: $documentCount traités</comment>");

                // Recharger l'utilisateur Admin et vider le cache après clear()
                $this->reloadAfterClear();

                // Forcer le garbage collector plus agressivement
                gc_collect_cycles();

                // Afficher l'utilisation mémoire actuelle
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                $output->writeln("<comment>Mémoire utilisée: " . round($memoryUsage, 2) . " MB</comment>");
            }
        }

        $this->em->flush();
        $output->writeln('<info>Documents migrés avec succès.</info>');

        // 5. Appliquer les visas des packages aux documents (temporairement désactivé)
        // $output->writeln('<info>Application des visas de packages...</info>');
        // $this->applyPackageVisasToDocuments($packageVisaData, $packageMapping, $output);

        $this->em->flush();
        $output->writeln('<info>Migration terminée.</info>');
        return Command::SUCCESS;
    }

    private function createMissingProjects(OutputInterface $output): void
    {
        // Récupérer tous les codes de projets uniques de l'ancienne base
        $projectCodes = $this->oldDb->fetchAllAssociative('SELECT DISTINCT Project FROM tbl_released_package WHERE Project IS NOT NULL');

        $createdCount = 0;
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];

            // Vérifier si le projet existe déjà
            $existingProject = $this->projects->findOneByOtp($otpCode);
            if (!$existingProject) {
                // Créer le projet manquant
                $project = new Project();
                $project->setOTP($otpCode);
                $project->setTitle("Projet migré: " . $otpCode);
                $project->setStatus('active');

                $this->em->persist($project);
                $createdCount++;

                $output->writeln(sprintf('<info>Projet créé: %s</info>', $otpCode));
            }
        }

        if ($createdCount > 0) {
            $this->em->flush();
            $output->writeln(sprintf('<info>%d projets créés.</info>', $createdCount));
        } else {
            $output->writeln('<info>Aucun projet à créer.</info>');
        }
    }

    private function isValidDate(?string $date): bool
    {
        if (empty($date)) {
            return false;
        }

        // Vérifier si la date contient des années négatives ou invalides
        if (strpos($date, '-0001') !== false || strpos($date, '0000') !== false) {
            return false;
        }

        try {
            $dateTime = new \DateTime($date);
            // Vérifier que l'année est raisonnable (après 1900)
            return $dateTime->format('Y') >= 1900;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function extractLastName(?string $fullName): ?string
    {
        if (empty($fullName)) {
            return null;
        }

        $fullName = trim($fullName);

        // Extraire le nom de famille (avant le premier espace)
        // Ex: "PISSOT T." -> "PISSOT", "GALIPAUD JF." -> "GALIPAUD"
        $parts = explode(' ', $fullName);
        return !empty($parts[0]) ? $parts[0] : null;
    }

    private function optimizeForMigration(OutputInterface $output): void
    {
        $output->writeln('<info>Application des optimisations mémoire...</info>');

        // 1. Augmenter la limite de mémoire PHP
        ini_set('memory_limit', '2G');
        $output->writeln('<comment>Limite mémoire PHP augmentée à 2G</comment>');

        // 2. Désactiver le mode debug de Doctrine pour réduire la consommation mémoire
        $config = $this->em->getConfiguration();
        if (method_exists($config, 'getSQLLogger') && $config->getSQLLogger()) {
            $config->setSQLLogger(null);
            $output->writeln('<comment>SQL Logger Doctrine désactivé</comment>');
        }

        // 3. Configurer le garbage collector
        gc_enable();
        $output->writeln('<comment>Garbage Collector activé</comment>');

        $output->writeln('<info>Optimisations appliquées avec succès.</info>');
    }

    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des données existantes...</info>');

        // Supprimer dans l'ordre inverse des dépendances
        // 1. Supprimer les visas (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Visa')->execute();
        $output->writeln('<comment>Visas supprimés.</comment>');

        // 2. Supprimer les commentaires (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Commentaire c WHERE c.documents IS NOT NULL')->execute();
        $output->writeln('<comment>Commentaires de documents supprimés.</comment>');

        // 3. Supprimer les documents (dépendent des packages)
        $this->em->createQuery('DELETE FROM App\Entity\Document')->execute();
        $output->writeln('<comment>Documents supprimés.</comment>');

        // 4. Supprimer les packages (dépendent des projets et utilisateurs)
        $this->em->createQuery('DELETE FROM App\Entity\ReleasedPackage')->execute();
        $output->writeln('<comment>Packages supprimés.</comment>');

        // 5. Supprimer les projets créés par migration (garder ceux créés manuellement)
        $this->em->createQuery('DELETE FROM App\Entity\Project p WHERE p.Title LIKE :pattern')
            ->setParameter('pattern', 'Projet migré:%')
            ->execute();
        $output->writeln('<comment>Projets migrés supprimés.</comment>');

        $this->em->flush();
        $output->writeln('<info>Nettoyage terminé.</info>');
    }

    private function initializeUserCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs...</info>');

        // Vider le cache
        $this->userCache = [];

        // Charger l'utilisateur Admin
        $this->adminUser = $this->users->findOneBy(['nom' => 'Admin']);
        if ($this->adminUser) {
            $output->writeln('<info>Utilisateur Admin trouvé pour fallback.</info>');
        } else {
            $output->writeln('<error>Aucun utilisateur Admin trouvé !</error>');
        }

        $output->writeln('<info>Cache initialisé.</info>');
    }

    private function findUserWithCache(?string $raw, OutputInterface $output): ?User
    {
        // Si vide, retourner null directement
        if (empty($raw)) {
            return null;
        }

        // Vérifier le cache d'abord
        $cacheKey = trim($raw);
        if (isset($this->userCache[$cacheKey])) {
            $cachedUser = $this->userCache[$cacheKey];
            if ($cachedUser === 'ADMIN_FALLBACK') {
                // Réduire la verbosité pour les cache hits
                return $this->adminUser;
            } elseif ($cachedUser === 'NOT_FOUND') {
                return null;
            } else {
                return $cachedUser;
            }
        }

        // Pas en cache, faire la recherche complète
        $output->writeln("<info>→ Recherche user pour « $raw » (pas en cache)</info>");
        $user = $this->findUserFlexible($raw, $output);

        // Mettre en cache le résultat
        if ($user === $this->adminUser) {
            $this->userCache[$cacheKey] = 'ADMIN_FALLBACK';
        } elseif ($user === null) {
            $this->userCache[$cacheKey] = 'NOT_FOUND';
        } else {
            $this->userCache[$cacheKey] = $user;
        }

        return $user;
    }

    private function reloadAfterClear(): void
    {
        // Recharger l'utilisateur Admin après clear()
        if ($this->adminUser) {
            $adminId = $this->adminUser->getId();
            $this->adminUser = $this->users->find($adminId);
        }

        // Vider le cache des utilisateurs car les entités ne sont plus valides
        $this->userCache = [];
    }

private function findUserFlexible(?string $raw, OutputInterface $output): ?User
{
    $output->writeln("<info>→ Recherche user pour « $raw »</info>");

    if (empty($raw)) {
        $output->writeln("  (raw vide)");
        return null;
    }

    // 1) Normalisation
    $clean = $this->normalizeName($raw);
    $output->writeln("  Normalisé ➜ « $clean »");

    $parts    = preg_split('/\s+/', $clean);
    $lastName = $parts[0] ?? '';
    $firstPart = $parts[1] ?? '';
    $output->writeln("  Nom extrait = « $lastName », initiales/prénom = « $firstPart »");

    // 2) Exact match
    $allUsers = $this->users->findAll();
    $output->writeln("  Total users en BDD: ".count($allUsers));

    $matches = [];
    foreach ($allUsers as $u) {
        if ($this->normalizeName($u->getNom()) === $lastName) {
            $matches[] = $u;
        }
    }
    $output->writeln("  Exact match nom: ".count($matches)." résultat(s)");

    if (count($matches) === 1) {
        $output->writeln("    → on retourne " . $matches[0]->getNom() . " " . $matches[0]->getPrenom());
        return $matches[0];
    }

    // 3) Initiales prénom
    if ($firstPart && count($matches) > 1) {
        $output->writeln("  Tentative correspondance initiales prénom…");
        foreach ($matches as $u) {
            $prenomNorm = $this->normalizeName($u->getPrenom() ?? '');
            $initials = '';
            foreach (preg_split('/\s+/', $prenomNorm) as $p) {
                $initials .= mb_substr($p, 0, 1, 'UTF-8');
            }
            if ($initials === $firstPart) {
                $output->writeln("    → matched by initials: " . $u->getNom() . " " . $u->getPrenom());
                return $u;
            }
        }
    }

    // 4) Inclusion partielle
    $output->writeln("  Recherche inclusion partielle du nom…");
    foreach ($allUsers as $u) {
        if (mb_stripos($this->normalizeName($u->getNom()), $lastName, 0, 'UTF-8') !== false) {
            $output->writeln("    → matched by contains: " . $u->getNom() . " " . $u->getPrenom());
            return $u;
        }
    }

    // 5) Fallback fuzzy (Levenshtein)
    $output->writeln("  Fallback fuzzy (Levenshtein)…");
    $closest = null;
    $minDist = PHP_INT_MAX;
    foreach ($allUsers as $u) {
        $dist = levenshtein($lastName, $this->normalizeName($u->getNom()));
        if ($dist < $minDist) {
            $minDist = $dist;
            $closest = $u;
        }
    }
    $output->writeln("    Distance min = $minDist pour ". $closest->getNom());
    if ($closest && $minDist <= 1) { // Seuil plus strict pour éviter les mauvaises correspondances
        $output->writeln("    → on accepte fuzzy: " . $closest->getNom() . " " . $closest->getPrenom());
        return $closest;
    }

    // 6) Fallback vers l'utilisateur Admin
    $output->writeln("    Pas de match, fallback vers Admin");
    if ($this->adminUser) {
        $output->writeln("    → fallback Admin: " . $this->adminUser->getNom() . " " . $this->adminUser->getPrenom());
        return $this->adminUser;
    }

    $output->writeln("    Aucun utilisateur Admin trouvé !");
    return null;
}

    private function setDocumentWorkflowState(Document $doc, array $legacyData, OutputInterface $output): void
    {
        // Déterminer l'état actuel basé sur les visas existants du document
        $currentState = $this->determineCurrentStateFromVisas($doc, $legacyData, $output);
        $stateTimestamps = [];
        $updates = [];

        // Créer les timestamps pour tous les états traversés
        $this->createStateTimestamps($doc, $legacyData, $currentState, $stateTimestamps, $updates);

        // Définir l'état actuel du document
        $doc->setCurrentSteps([$currentState => true]);
        $doc->setStateTimestamps($stateTimestamps);
        $doc->setUpdates($updates);

        $output->writeln("<comment>Document {$legacyData['Reference']} → État: $currentState</comment>");
    }

    private function determineCurrentStateFromVisas(Document $doc, array $legacyData, OutputInterface $output): string
    {
        // Récupérer tous les visas du document (incluant ceux créés par la migration)
        $documentVisas = [];
        foreach ($doc->getVisas() as $visa) {
            if ($visa->getStatus() === 'valid') {
                $documentVisas[] = $visa->getName();
            }
        }

        // Nouvelle approche: trouver le visa le plus avancé, puis déterminer l'état suivant
        $completedStates = $this->getCompletedStatesFromVisas($documentVisas);

        // Déterminer l'état actuel basé sur les états complétés et les conditions du document
        return $this->determineNextStateAfterCompletion($completedStates, $legacyData);
    }

    private function getCompletedStatesFromVisas(array $documentVisas): array
    {
        // Mapping des visas vers les états qu'ils complètent
        $visaToStateMapping = [
            'visa_BE_0' => 'BE_0',
            'visa_BE_1' => 'BE_1',
            'visa_BE' => 'BE',
            'visa_produit' => 'Produit',
            'visa_Project' => 'Project',
            'visa_Quality' => 'Quality',
            'visa_Qual_Logistique' => 'Qual_Logistique',
            'visa_Logistique' => 'Logistique',
            'visa_Achat_Rfq' => 'Achat_Rfq',
            'visa_Achat_F30' => 'Achat_F30',
            'visa_Achat_FIA' => 'Achat_FIA',
            'visa_Achat_Hts' => 'Achat_Hts',
            'visa_SaisieHts' => 'Saisie_hts',
            'visa_prod' => ['Assembly', 'Machining', 'Molding'], // visa_prod peut compléter plusieurs états
            'visa_Methode_assemblage' => 'Methode_assemblage',
            'visa_Planning' => 'Planning',
            'visa_Metro' => 'Metro',
            'visa_Indus' => 'Indus',
            'visa_methode_Labo' => 'methode_Labo',
            'visa_QProd' => 'QProd',
            'visa_Core_Data' => 'Core_Data',
            'visa_Prod_Data' => 'Prod_Data',
            'visa_GID' => 'GID',
            'visa_Costing' => 'Costing',
            'visa_Tirage_Plans' => 'Tirage_Plans',
        ];

        $completedStates = [];
        foreach ($documentVisas as $visa) {
            if (isset($visaToStateMapping[$visa])) {
                $states = $visaToStateMapping[$visa];
                if (is_array($states)) {
                    $completedStates = array_merge($completedStates, $states);
                } else {
                    $completedStates[] = $states;
                }
            }
        }

        return array_unique($completedStates);
    }

    private function determineNextStateAfterCompletion(array $completedStates, array $legacyData): string
    {
        // Nouvelle logique: un document est dans l'état qu'il est prêt à traiter,
        // pas dans l'état qu'il a déjà complété

        // Si le document a complété Costing, il est terminé (ne devrait pas être dans un workflow actif)
        if (in_array('Costing', $completedStates)) {
            // Pour les statistiques, on peut le placer dans Costing mais il sera filtré des vues actives
            return 'Costing';
        }

        // Si le document a complété GID mais pas Costing, il est prêt pour Costing
        if (in_array('GID', $completedStates) && !in_array('Costing', $completedStates)) {
            return 'Costing';
        }

        // Si le document a complété Prod_Data mais pas GID, il est prêt pour GID
        if (in_array('Prod_Data', $completedStates) && !in_array('GID', $completedStates)) {
            return 'GID';
        }

        // Si le document a complété Core_Data mais pas Prod_Data, il est prêt pour Prod_Data (SAP Prod)
        if (in_array('Core_Data', $completedStates) && !in_array('Prod_Data', $completedStates)) {
            return 'Prod_Data';
        }

        // Si le document a complété Planning/Metro/Methode_assemblage/Indus mais pas Core_Data, il est prêt pour Core_Data (SAP Core)
        if ((in_array('Planning', $completedStates) || in_array('Metro', $completedStates) ||
             in_array('Methode_assemblage', $completedStates) || in_array('Indus', $completedStates)) &&
            !in_array('Core_Data', $completedStates)) {
            return 'Core_Data';
        }

        // Si le document a complété un état de production mais pas les suivants
        if (in_array('Assembly', $completedStates) || in_array('Machining', $completedStates) ||
            in_array('Molding', $completedStates)) {

            $docType = $legacyData['Doc_Type'] ?? '';

            // Déterminer le prochain état selon le type de document
            if ($docType === 'ASSY' && !in_array('Methode_assemblage', $completedStates)) {
                return 'Methode_assemblage';
            } elseif (in_array($docType, ['MACH', 'MOLD']) && !in_array('Planning', $completedStates)) {
                return 'Planning';
            } elseif (!in_array('Core_Data', $completedStates)) {
                return 'Core_Data'; // Fallback vers Core_Data
            }
        }

        // Si le document a complété les états d'achat mais pas Core_Data
        if ((in_array('Achat_Hts', $completedStates) || in_array('Achat_FIA', $completedStates) ||
             in_array('Achat_F30', $completedStates) || in_array('Achat_Rfq', $completedStates)) &&
            !in_array('Core_Data', $completedStates)) {
            return 'Core_Data';
        }

        // Si le document a complété Logistique mais pas Core_Data
        if (in_array('Logistique', $completedStates) && !in_array('Core_Data', $completedStates)) {
            return 'Core_Data';
        }

        // Si le document a complété Quality ou Project
        if (in_array('Quality', $completedStates) || in_array('Project', $completedStates)) {
            $docType = $legacyData['Doc_Type'] ?? '';

            // Déterminer le prochain état selon le type
            if ($docType === 'PUR' && !in_array('Achat_Rfq', $completedStates)) {
                return 'Achat_Rfq';
            } elseif ($docType === 'ASSY' && !in_array('Assembly', $completedStates)) {
                return 'Assembly';
            } elseif ($docType === 'MACH' && !in_array('Machining', $completedStates)) {
                return 'Machining';
            } elseif ($docType === 'MOLD' && !in_array('Molding', $completedStates)) {
                return 'Molding';
            } else {
                return 'Core_Data'; // Fallback pour DOC, etc.
            }
        }

        // Si le document a complété Produit mais pas Quality
        if (in_array('Produit', $completedStates) && !in_array('Quality', $completedStates)) {
            return 'Quality';
        }

        // Si le document a complété BE mais pas Produit
        if (in_array('BE', $completedStates) && !in_array('Produit', $completedStates)) {
            return 'Produit';
        }

        // Si le document a complété BE_1 mais pas BE
        if (in_array('BE_1', $completedStates) && !in_array('BE', $completedStates)) {
            return 'BE';
        }

        // Si le document a complété BE_0 mais pas BE_1
        if (in_array('BE_0', $completedStates) && !in_array('BE_1', $completedStates)) {
            return 'BE_1';
        }

        // Par défaut, commencer par BE_0
        return 'BE_0';
    }

    private function determineFinalStateForCompletedDocument(array $legacyData): string
    {
        // Pour les documents terminés (avec visa_Costing), on peut les placer dans un état final
        // approprié pour les statistiques, mais ils ne seront pas visibles dans les workflows actifs

        $docType = $legacyData['Doc_Type'] ?? '';

        // Retourner un état final logique selon le type de document
        if ($docType === 'PUR') {
            return 'Core_Data'; // Les documents PUR finissent généralement à Core_Data
        } elseif (in_array($docType, ['ASSY', 'MACH', 'MOLD'])) {
            return 'Costing'; // Les documents de production finissent à Costing
        } else {
            return 'Core_Data'; // Fallback pour DOC, etc.
        }
    }

    private function canDocumentBeInState(string $state, array $documentVisas, array $legacyData): bool
    {
        // Récupérer les règles de visa pour cet état depuis VisaChecker
        $rules = $this->getVisaRulesForState($state);

        // Vérifier que le document a tous les visas requis
        if (!empty($rules['required'])) {
            foreach ($rules['required'] as $requiredVisa) {
                if (!in_array($requiredVisa, $documentVisas)) {
                    return false; // Manque un visa requis
                }
            }
        }

        // Vérifier qu'aucun visa interdit n'est présent
        if (!empty($rules['forbidden'])) {
            foreach ($rules['forbidden'] as $forbiddenVisa) {
                if (in_array($forbiddenVisa, $documentVisas)) {
                    return false; // A un visa interdit (donc a dépassé cet état)
                }
            }
        }

        // Vérifier les conditions optionnelles (OR)
        if (!empty($rules['optional_or'])) {
            $hasOptionalVisa = false;
            foreach ($rules['optional_or'] as $optionalVisa) {
                if (in_array($optionalVisa, $documentVisas)) {
                    $hasOptionalVisa = true;
                    break;
                }
            }
            if (!$hasOptionalVisa) {
                return false; // Aucun visa optionnel requis
            }
        }

        // Vérifier les conditions spécifiques selon l'état
        return $this->checkStateSpecificConditions($state, $legacyData);
    }

    private function getVisaRulesForState(string $state): array
    {
        // Copie des règles de VisaChecker.php pour déterminer les conditions d'entrée dans chaque état
        $visaRules = [
            'BE_1' => [
                'required' => ['visa_BE_0'],
                'forbidden' => ['visa_BE_1'],
                'optional_or' => [],
            ],
            'BE' => [
                'required' => ['visa_BE_1'],
                'forbidden' => ['visa_BE'],
                'optional_or' => [],
            ],
            'Produit' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_produit'],
                'optional_or' => [],
            ],
            'Project' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Project'],
                'optional_or' => [],
            ],
            'Quality' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Quality'],
                'optional_or' => [],
            ],
            'Qual_Logistique' => [
                'required' => ['visa_BE'],
                'forbidden' => ['visa_Qual_Logistique'],
                'optional_or' => [],
            ],
            'Logistique' => [
                'required' => ['visa_Qual_Logistique'],
                'forbidden' => ['visa_Logistique'],
                'optional_or' => [],
            ],
            'Achat_Rfq' => [
                'required' => ['visa_Quality', 'visa_produit'],
                'forbidden' => ['visa_Achat_Rfq'],
                'optional_or' => [],
            ],
            'Achat_F30' => [
                'required' => ['visa_Achat_Rfq'],
                'forbidden' => ['visa_Achat_F30'],
                'optional_or' => [],
            ],
            'Assembly' => [
                'required' => ['visa_produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],
            'Machining' => [
                'required' => ['visa_produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],
            'Molding' => [
                'required' => ['visa_produit'],
                'forbidden' => ['visa_prod'],
                'optional_or' => [],
            ],
            'Methode_assemblage' => [
                'required' => ['visa_prod'],
                'forbidden' => ['visa_Methode_assemblage'],
                'optional_or' => [],
            ],
            'Planning' => [
                'required' => ['visa_prod'],
                'forbidden' => ['visa_Planning'],
                'optional_or' => [],
            ],
            'Metro' => [
                'required' => [],
                'forbidden' => ['visa_Metro'],
                'optional_or' => ['visa_prod', 'visa_Quality'],
            ],
            'Achat_FIA' => [
                'required' => ['visa_Achat_Rfq', 'visa_Achat_F30'],
                'forbidden' => ['visa_Achat_FIA'],
                'optional_or' => [],
            ],
            'Achat_Hts' => [
                'required' => ['visa_Achat_FIA'],
                'forbidden' => ['visa_Achat_Hts'],
                'optional_or' => [],
            ],
            'Saisie_hts' => [
                'required' => ['visa_Achat_Hts'],
                'forbidden' => ['visa_SaisieHts'],
                'optional_or' => [],
            ],
            'methode_Labo' => [
                'required' => ['visa_Indus'],
                'forbidden' => ['visa_methode_Labo'],
                'optional_or' => [],
            ],
            'QProd' => [
                'required' => ['visa_Metro'],
                'forbidden' => ['visa_QProd'],
                'optional_or' => [],
            ],
            'Indus' => [
                'required' => ['visa_prod'],
                'forbidden' => ['visa_Indus'],
                'optional_or' => ['visa_Metro'],
            ],
            'Core_Data' => [
                'required' => [],
                'forbidden' => ['visa_Core_Data'],
                'optional_or' => ['visa_Achat_Rfq', 'visa_Achat_F30', 'visa_Metro', 'visa_Planning', 'visa_Quality', 'visa_Logistique'],
            ],
            'Prod_Data' => [
                'required' => ['visa_Core_Data'],
                'forbidden' => ['visa_Prod_Data'],
                'optional_or' => [],
            ],
            'Costing' => [
                'required' => [],
                'forbidden' => ['visa_Costing'],
                'optional_or' => ['visa_GID', 'visa_Achat_FIA'],
            ],
            'GID' => [
                'required' => ['visa_Prod_Data'],
                'forbidden' => ['visa_GID'],
                'optional_or' => ['visa_prod', 'visa_Indus'],
            ],
            'Tirage_Plans' => [
                'required' => ['visa_Core_Data'],
                'forbidden' => ['visa_Tirage_Plans'],
                'optional_or' => [],
            ],
        ];

        return $visaRules[$state] ?? ['required' => [], 'forbidden' => [], 'optional_or' => []];
    }

    private function checkStateSpecificConditions(string $state, array $legacyData): bool
    {
        // Vérifier les conditions spécifiques selon l'état (basé sur WorkflowGuardListener)
        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';
        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $switchAletiq = (bool)($legacyData['SWITCH_ALETIQ'] ?? false);

        switch ($state) {
            case 'Project':
                // Project pour GA/FT dans projets non-STAND
                $relPackProject = 'STAND'; // Valeur par défaut, sera mise à jour si nécessaire
                return (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT')) && $relPackProject !== 'STAND';

            case 'Quality':
                // Quality pour PUR/ASSY/DOC/MOLD
                return in_array($docType, ['PUR', 'ASSY', 'DOC', 'MOLD']);

            case 'Qual_Logistique':
                // Qual_Logistique pour documents avec impact inventaire
                return $docType !== 'DOC' && $inventoryImpact !== 'NO IMPACT';

            case 'Assembly':
                return $docType === 'ASSY';

            case 'Machining':
                return $docType === 'MACH';

            case 'Molding':
                return $docType === 'MOLD';

            case 'Achat_Rfq':
                return $docType === 'PUR' && $procType === 'F';

            case 'Achat_F30':
                return $docType === 'PUR' && $procType === 'F30';

            case 'Tirage_Plans':
                return in_array($docType, ['MACH', 'MOLD']) && $switchAletiq;

            default:
                // Pour les autres états, pas de conditions spécifiques
                return true;
        }
    }

    private function determineCurrentStateFromLegacy(Document $doc, array $legacyData): string
    {
        // Récupérer les données nécessaires
        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';
        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $materialType = $legacyData['Material_Type'] ?? '';

        // Récupérer le projet du package
        $relPackProject = 'STAND';
        if (isset($legacyData['Rel_Pack_Num'])) {
            try {
                $packageData = $this->oldDb->fetchAssociative(
                    'SELECT Project FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                    [$legacyData['Rel_Pack_Num']]
                );
                if ($packageData && !empty($packageData['Project'])) {
                    $relPackProject = $packageData['Project'];
                }
            } catch (\Exception $e) {
                // En cas d'erreur, garder la valeur par défaut
            }
        }

        // Déterminer l'état actuel basé sur les conditions legacy
        // Les documents terminés gardent leur état final, ils seront filtrés dans l'UI
        // Vérifier dans l'ordre du workflow (du plus avancé au moins avancé)

        // États finaux - Vérifier d'abord les états les plus avancés
        if ($this->checkTiragePlansCondition($legacyData)) {
            return 'Tirage_Plans';
        }

        if ($this->checkCostingCondition($legacyData)) {
            return 'Costing';
        }

        if ($this->checkGIDCondition($legacyData)) {
            return 'GID';
        }

        if ($this->checkProdDataCondition($legacyData)) {
            return 'Prod_Data';
        }

        if ($this->checkCoreDataCondition($legacyData)) {
            return 'Core_Data';
        }

        // États logistiques
        if ($this->checkLogistiqueCondition($legacyData)) {
            return 'Logistique';
        }

        if ($this->checkQualLogistiqueCondition($legacyData)) {
            return 'Qual_Logistique';
        }

        // États d'achat
        if ($this->checkSaisieHtsCondition($legacyData)) {
            return 'Saisie_hts';
        }

        if ($this->checkAchatHtsCondition($legacyData)) {
            return 'Achat_Hts';
        }

        if ($this->checkAchatFIACondition($legacyData)) {
            return 'Achat_FIA';
        }

        if ($this->checkAchatF30Condition($legacyData)) {
            return 'Achat_F30';
        }

        if ($this->checkAchatRfqCondition($legacyData)) {
            return 'Achat_Rfq';
        }

        // QProd - Pour MACH/MOLD après Metro
        if ($this->checkQProdCondition($legacyData)) {
            return 'QProd';
        }

        // methode_Labo - Pour ASSY après Indus
        if ($this->checkMethodeLaboCondition($legacyData)) {
            return 'methode_Labo';
        }

        // Indus - Conditions selon le type
        if ($this->checkIndusCondition($legacyData)) {
            return 'Indus';
        }

        // Metro - Conditions selon procType et docType
        if ($this->checkMetroCondition($legacyData)) {
            return 'Metro';
        }

        // Planning - Pour ASSY/MACH/MOLD/DOC après prod
        if ($this->checkPlanningCondition($legacyData)) {
            return 'Planning';
        }

        // Methode_assemblage - Pour ASSY/DOC/PACKAGING après prod
        if ($this->checkMethodeAssemblageCondition($legacyData)) {
            return 'Methode_assemblage';
        }

        // États de production selon le type de document
        if ($this->checkAssemblyCondition($legacyData)) {
            return 'Assembly';
        }

        if ($this->checkMachiningCondition($legacyData)) {
            return 'Machining';
        }

        if ($this->checkMoldingCondition($legacyData)) {
            return 'Molding';
        }

        // Quality - Pour PUR/ASSY/DOC/MOLD
        if ($this->checkQualityCondition($legacyData)) {
            return 'Quality';
        }

        // Project - Pour GA/FT dans projets non-STAND
        if ($this->checkProjectCondition($legacyData)) {
            return 'Project';
        }

        // Produit - Premier état après BE
        if ($this->checkProduitCondition($legacyData)) {
            return 'Produit';
        }

        // États BE
        if ($this->checkBECondition($legacyData)) {
            return 'BE';
        }

        if ($this->checkBE1Condition($legacyData)) {
            return 'BE_1';
        }

        // Par défaut BE_0
        return 'BE_0';
    }

    // Méthodes de vérification des conditions legacy basées sur condition_sql.php

    private function isDocumentCompleted(array $legacyData): bool
    {
        // Un document est considéré comme terminé s'il a atteint l'un des états finaux
        // Basé sur les visas finaux du système legacy

        // VISA_Finance = Costing terminé (état final pour la plupart des documents)
        if (!empty($legacyData['VISA_Finance'])) {
            return true;
        }

        // Pour les documents qui n'ont pas VISA_Finance, vérifier d'autres états finaux
        $docType = $legacyData['Doc_Type'] ?? '';

        // VISA_ROUTING_ENTRY = Routing Entry terminé (état final pour ASSY/MACH/MOLD sans Finance)
        if (!empty($legacyData['VISA_ROUTING_ENTRY']) && in_array($docType, ['ASSY', 'MACH', 'MOLD'])) {
            return true;
        }

        // Pour les documents PUR sans Finance, vérifier VISA_PUR_5 (HTS final)
        if ($docType === 'PUR' && !empty($legacyData['VISA_PUR_5'])) {
            return true;
        }

        // VISA_GID_2 seul n'indique pas forcément un document terminé
        // car il peut encore aller vers ROUTING_ENTRY puis Finance

        return false;
    }

    private function checkBE0Condition(array $legacyData): bool
    {
        // BE_0 est l'état initial - un document y est s'il n'a aucun visa significatif
        // Dans le système legacy, cela correspond à un document qui vient d'être créé

        // Vérifier qu'aucun visa principal n'est présent
        $mainVisas = [
            'VISA_Product', 'VISA_Quality', 'VISA_Project', 'VISA_Inventory',
            'VISA_Prod', 'VISA_Method', 'VISA_Supply', 'VISA_Metro',
            'VISA_MOF', 'VISA_PUR_1', 'VISA_PUR_2', 'VISA_PUR_3'
        ];

        foreach ($mainVisas as $visa) {
            if (!empty($legacyData[$visa])) {
                return false;
            }
        }

        return true;
    }

    private function checkTiragePlansCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $switchAletiq = $legacyData['SWITCH_ALETIQ'] ?? false;

        // Tirage_Plans nécessite MACH/MOLD + switch aletiq + visa Core_Data
        return in_array($docType, ['MACH', 'MOLD'], true)
            && $switchAletiq
            && !empty($legacyData['VISA_GID_2']) // GID_2 correspond à Core_Data dans legacy
            && empty($legacyData['VISA_Finance']); // Pas encore terminé
    }

    private function checkCostingCondition(array $legacyData): bool
    {
        // Costing (Finance dans legacy) - Un document est en Costing s'il a VISA_Finance
        return !empty($legacyData['VISA_Finance']);
    }

    private function checkGIDCondition(array $legacyData): bool
    {
        // GID - Un document est en GID s'il a VISA_GID mais pas GID_2 ni Finance
        return !empty($legacyData['VISA_GID'])
            && empty($legacyData['VISA_GID_2'])
            && empty($legacyData['VISA_Finance']);
    }

    private function checkProjectConditionForGID(array $legacyData): bool
    {
        // Récupérer le projet depuis la base legacy
        $project = 'STAND';
        if (isset($legacyData['Rel_Pack_Num'])) {
            try {
                $packageData = $this->oldDb->fetchAssociative(
                    'SELECT Project FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                    [$legacyData['Rel_Pack_Num']]
                );
                if ($packageData && !empty($packageData['Project'])) {
                    $project = $packageData['Project'];
                }
            } catch (\Exception $e) {
                // En cas d'erreur, garder la valeur par défaut
            }
        }

        $prodDraw = $legacyData['Prod_Draw'] ?? '';
        $visaProject = $legacyData['VISA_Project'] ?? '';

        $condition1 = $project !== 'STAND' && !empty($visaProject)
            && (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'));
        $condition2 = $project !== 'STAND' && empty($visaProject)
            && !(str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'));
        $condition3 = $project === 'STAND' && empty($visaProject);

        return $condition1 || $condition2 || $condition3;
    }

    private function checkInventoryConditionForGID(array $legacyData): bool
    {
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';
        $docType = $legacyData['Doc_Type'] ?? '';
        $visaInventory = $legacyData['VISA_Inventory'] ?? '';

        $condition1 = !empty($visaInventory)
            && (in_array($inventoryImpact, ['TO BE SCRAPPED', 'TO BE UPDATED'], true));
        $condition2 = empty($visaInventory)
            && (in_array($inventoryImpact, ['NO IMPACT'], true) || $docType === 'DOC');

        return $condition1 || $condition2;
    }

    private function checkProdDataCondition(array $legacyData): bool
    {
        // Prod_Data correspond à GID_2 dans le système legacy
        return !empty($legacyData['VISA_GID']) && empty($legacyData['VISA_GID_2']);
    }

    private function checkCoreDataCondition(array $legacyData): bool
    {
        // Core_Data correspond à GID_2 dans le système legacy
        // Un document est en Core_Data s'il a GID_2 mais pas Finance
        return !empty($legacyData['VISA_GID_2']) && empty($legacyData['VISA_Finance']);
    }

    private function checkAchatFIACondition(array $legacyData): bool
    {
        $procType = $legacyData['Proc_Type'] ?? '';

        // Achat_FIA pour procType F/F30 après Achat_Rfq et Achat_F30
        return in_array($procType, ['F', 'F30'], true)
            && !empty($legacyData['VISA_PUR_1'])
            && !empty($legacyData['VISA_PUR_2'])
            && empty($legacyData['VISA_PUR_3']);
    }

    private function checkAchatHtsCondition(array $legacyData): bool
    {
        // Achat_Hts après Achat_FIA
        return !empty($legacyData['VISA_PUR_3']) && empty($legacyData['VISA_PUR_5']);
    }

    private function checkSaisieHtsCondition(array $legacyData): bool
    {
        // Saisie_hts après Achat_Hts
        return !empty($legacyData['VISA_PUR_5']) && empty($legacyData['VISA_SaisieHts']);
    }

    private function checkAchatF30Condition(array $legacyData): bool
    {
        $procType = $legacyData['Proc_Type'] ?? '';

        // Achat_F30 pour procType F30 après Achat_Rfq
        return $procType === 'F30'
            && !empty($legacyData['VISA_PUR_1'])
            && empty($legacyData['VISA_PUR_2']);
    }

    private function checkAchatRfqCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';

        // Achat_Rfq pour documents PUR non-E après Quality et Product
        return $docType === 'PUR'
            && !in_array($procType, ['E'], true)
            && !empty($legacyData['VISA_Quality'])
            && !empty($legacyData['VISA_Product'])
            && empty($legacyData['VISA_PUR_1']);
    }

    private function checkQProdCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // QProd pour MACH/MOLD après Metro
        return in_array($docType, ['MACH', 'MOLD'], true)
            && !empty($legacyData['VISA_METRO'])
            && empty($legacyData['VISA_Q_PROD']);
    }

    private function checkMethodeLaboCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // Methode_Labo pour ASSY après Indus (MOF dans legacy)
        return $docType === 'ASSY'
            && !empty($legacyData['VISA_MOF'])
            && empty($legacyData['VISA_LABO']);
    }

    private function checkIndusCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // Indus (MOF dans legacy) selon les conditions MOF_Conditions
        $condition1 = $docType === 'ASSY' && !empty($legacyData['VISA_METRO']);
        $condition2 = $docType === 'DOC';

        return ($condition1 || $condition2)
            && !empty($legacyData['VISA_Prod'])
            && empty($legacyData['VISA_MOF']);
    }

    private function checkMetroCondition(array $legacyData): bool
    {
        // Metro - Un document est en Metro s'il a VISA_Metro mais pas les visas suivants
        return !empty($legacyData['VISA_Metro'])
            && empty($legacyData['VISA_Supply'])
            && empty($legacyData['VISA_GID'])
            && empty($legacyData['VISA_Finance']);
    }

    private function checkPlanningCondition(array $legacyData): bool
    {
        // Planning (Supply dans legacy) - Un document est en Planning s'il a VISA_Supply mais pas les visas suivants
        return !empty($legacyData['VISA_Supply'])
            && empty($legacyData['VISA_GID'])
            && empty($legacyData['VISA_Finance']);
    }

    private function checkMethodeAssemblageCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $materialType = $legacyData['Material_Type'] ?? '';

        // Methode_assemblage (Method dans legacy) pour ASSY/DOC/PACKAGING après prod
        return ((in_array($docType, ['ASSY', 'DOC'], true) || str_contains($materialType, 'PACKAGING'))
            && !empty($legacyData['VISA_Prod'])
            && empty($legacyData['VISA_Method']));
    }

    private function checkAssemblyCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $procType = $legacyData['Proc_Type'] ?? '';

        // Assembly (Prod_ASSY dans legacy) pour ASSY/DOC non-F après Product
        return ((in_array($docType, ['ASSY', 'DOC'], true) && !str_starts_with($procType, 'F'))
            && !empty($legacyData['VISA_Product'])
            && empty($legacyData['VISA_Prod']));
    }

    private function checkMachiningCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // Machining (Prod_MACH dans legacy) pour MACH après Product
        return $docType === 'MACH'
            && !empty($legacyData['VISA_Product'])
            && empty($legacyData['VISA_Prod']);
    }

    private function checkMoldingCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // Molding (Prod_MOLD dans legacy) pour MOLD après Product
        return $docType === 'MOLD'
            && !empty($legacyData['VISA_Product'])
            && empty($legacyData['VISA_Prod']);
    }

    private function checkLogistiqueCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';

        // Logistique après Qual_Logistique (Inventory dans legacy)
        return $docType !== 'DOC'
            && $inventoryImpact !== 'NO IMPACT'
            && !empty($legacyData['VISA_Inventory'])
            && empty($legacyData['VISA_Logistique']);
    }

    private function checkQualLogistiqueCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';
        $inventoryImpact = $legacyData['Inventory_Impact'] ?? '';

        // Qual_Logistique (Inventory dans legacy) pour documents avec impact inventaire
        return $docType !== 'DOC'
            && $inventoryImpact !== 'NO IMPACT'
            && !empty($legacyData['VISA_BE_3'])
            && empty($legacyData['VISA_Inventory']);
    }

    private function checkQualityCondition(array $legacyData): bool
    {
        $docType = $legacyData['Doc_Type'] ?? '';

        // Quality pour PUR/ASSY/DOC/MOLD après BE
        return in_array($docType, ['PUR', 'ASSY', 'DOC', 'MOLD'], true)
            && !empty($legacyData['VISA_BE_3'])
            && empty($legacyData['VISA_Quality']);
    }

    private function checkProjectCondition(array $legacyData): bool
    {
        $prodDraw = $legacyData['Prod_Draw'] ?? '';

        // Récupérer le projet depuis la base legacy
        $project = 'STAND';
        if (isset($legacyData['Rel_Pack_Num'])) {
            try {
                $packageData = $this->oldDb->fetchAssociative(
                    'SELECT Project FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                    [$legacyData['Rel_Pack_Num']]
                );
                if ($packageData && !empty($packageData['Project'])) {
                    $project = $packageData['Project'];
                }
            } catch (\Exception $e) {
                // En cas d'erreur, garder la valeur par défaut
            }
        }

        // Project pour GA/FT dans projets non-STAND selon Project_Conditions
        return (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
            && $project !== 'STAND'
            && empty($legacyData['VISA_Project']);
    }

    private function checkProduitCondition(array $legacyData): bool
    {
        // Produit (Product dans legacy) - documents qui ont besoin du visa Product
        // Basé sur Product_Conditions du fichier legacy
        return empty($legacyData['VISA_Product']) && !$this->checkBE0Condition($legacyData);
    }

    private function checkBECondition(array $legacyData): bool
    {
        // BE correspond à l'état où le document a passé les validations initiales
        // mais n'a pas encore de visa Product
        // Dans le système legacy, cela correspond aux documents avec VISA_Inventory
        return !empty($legacyData['VISA_Inventory']) && empty($legacyData['VISA_Product']);
    }

    private function checkBE1Condition(array $legacyData): bool
    {
        // BE_1 est un état intermédiaire - documents qui ont quelques visas mais pas Inventory
        // Vérifier s'il y a des visas mais pas encore VISA_Inventory
        $hasAnyVisa = !empty($legacyData['VISA_Quality']) || !empty($legacyData['VISA_Project']);
        return $hasAnyVisa && empty($legacyData['VISA_Inventory']) && empty($legacyData['VISA_Product']);
    }



    private function createStateTimestamps(Document $doc, array $legacyData, string $currentState, array &$stateTimestamps, array &$updates): void
    {
        // Mapping des visas legacy vers les noms de visas dans le système
        // Basé sur le mapping utilisé dans la migration des visas (lignes 269-279)
        $legacyVisaMapping = [
            'VISA_Project' => 'visa_project',
            'VISA_Inventory' => 'visa_inventory', // Correspond à BE dans le nouveau système
            'VISA_Product' => 'visa_product',
            'VISA_Quality' => 'visa_quality',
            'VISA_Method' => 'visa_method',
            'VISA_Finance' => 'visa_finance',
            'VISA_Prod' => 'visa_prod',
            'VISA_Supply' => 'visa_supply',
            'VISA_PUR_1' => 'visa_pur_1',
        ];

        // Créer les timestamps basés sur les visas legacy
        foreach ($legacyVisaMapping as $legacyVisa => $systemVisa) {
            $dateCol = str_replace('VISA_', 'DATE_', $legacyVisa);

            if (isset($legacyData[$legacyVisa]) && !empty($legacyData[$legacyVisa]) &&
                isset($legacyData[$dateCol]) && $this->isValidDate($legacyData[$dateCol])) {

                $visaDate = $legacyData[$dateCol];

                // Déterminer l'état correspondant au visa
                $stateForVisa = $this->getStateForLegacyVisa($legacyVisa);
                if ($stateForVisa) {
                    $stateTimestamps[$stateForVisa] = [[
                        'enter' => $visaDate,
                        'exit' => null,
                        'from_state' => null
                    ]];

                    $updates[] = [
                        'type' => 'visa',
                        'date' => $visaDate,
                        'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                        'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                        'details' => "Migration du visa $legacyVisa"
                    ];
                }
            }
        }

        // Ajouter les timestamps pour les visas BE legacy
        $this->addBETimestamps($legacyData, $stateTimestamps, $updates);

        // Ajouter une entrée pour l'état actuel si pas déjà présent
        if (!isset($stateTimestamps[$currentState])) {
            $creationDate = $legacyData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? date('Y-m-d H:i:s');

            $stateTimestamps[$currentState] = [[
                'enter' => $creationDate,
                'exit' => null,
                'from_state' => null
            ]];

            $updates[] = [
                'type' => 'creation',
                'date' => $creationDate,
                'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                'details' => 'Document créé lors de la migration'
            ];
        }
    }

    private function getStateForLegacyVisa(string $legacyVisaName): ?string
    {
        // Mapping des visas legacy vers les états correspondants
        $legacyVisaToStateMapping = [
            'VISA_Project' => 'Project',
            'VISA_Inventory' => 'BE', // VISA_Inventory correspond à l'état BE
            'VISA_Product' => 'Produit',
            'VISA_Quality' => 'Quality',
            'VISA_Method' => 'Methode_assemblage',
            'VISA_Finance' => 'Costing',
            'VISA_Prod' => 'Assembly', // Générique, peut être Assembly/Machining/Molding
            'VISA_Supply' => 'Planning',
            'VISA_PUR_1' => 'Achat_Rfq',
        ];

        return $legacyVisaToStateMapping[$legacyVisaName] ?? null;
    }

    private function addBETimestamps(array $legacyData, array &$stateTimestamps, array &$updates): void
    {
        // Ajouter les timestamps pour les états BE basés sur les visas legacy disponibles

        // BE_0 - Toujours présent (création du document)
        $creationDate = $legacyData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? date('Y-m-d H:i:s');
        if ($this->isValidDate($creationDate)) {
            $stateTimestamps['BE_0'] = [[
                'enter' => $creationDate,
                'exit' => null,
                'from_state' => null
            ]];
        }

        // BE_1 - Basé sur les premiers visas (Quality ou Project)
        $firstVisaDate = null;
        $firstVisaName = null;

        if (!empty($legacyData['VISA_Quality']) && isset($legacyData['DATE_Quality']) && $this->isValidDate($legacyData['DATE_Quality'])) {
            $firstVisaDate = $legacyData['DATE_Quality'];
            $firstVisaName = 'VISA_Quality';
        } elseif (!empty($legacyData['VISA_Project']) && isset($legacyData['DATE_Project']) && $this->isValidDate($legacyData['DATE_Project'])) {
            $firstVisaDate = $legacyData['DATE_Project'];
            $firstVisaName = 'VISA_Project';
        }

        if ($firstVisaDate) {
            $stateTimestamps['BE_1'] = [[
                'enter' => $firstVisaDate,
                'exit' => null,
                'from_state' => 'BE_0'
            ]];

            $updates[] = [
                'type' => 'visa',
                'date' => $firstVisaDate,
                'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                'details' => "Migration du visa $firstVisaName"
            ];
        }

        // BE - Basé sur VISA_Inventory
        if (!empty($legacyData['VISA_Inventory']) && isset($legacyData['DATE_Inventory']) && $this->isValidDate($legacyData['DATE_Inventory'])) {
            $stateTimestamps['BE'] = [[
                'enter' => $legacyData['DATE_Inventory'],
                'exit' => null,
                'from_state' => 'BE_1'
            ]];

            $updates[] = [
                'type' => 'visa',
                'date' => $legacyData['DATE_Inventory'],
                'user_id' => $this->adminUser ? $this->adminUser->getId() : null,
                'user_name' => $this->adminUser ? $this->adminUser->getPrenom() . ' ' . $this->adminUser->getNom() : 'Migration',
                'details' => 'Migration du visa VISA_Inventory'
            ];
        }
    }

    private function applyPackageVisasToDocuments(array $packageVisaData, array $packageMapping, OutputInterface $output): void
    {
        // Appliquer les visas des packages à tous leurs documents
        // 1. No visas in legacy → No visas in new system
        // 2. Has Creation_VISA only → Add visa_be_0 to all documents
        // 3. Has Creation_VISA + VISA_BE_2 → Add visa_be_0 + visa_be_1 to all documents
        // 4. Has Creation_VISA + VISA_BE_2 + VISA_BE_3 → Add visa_be_0 + visa_be_1 + visa_be to all documents

        $processedPackages = 0;
        $processedDocuments = 0;

        foreach ($packageVisaData as $packageNum => $legacyData) {
            // Récupérer les documents qui appartiennent à ce package legacy
            // Utiliser une requête SQL directe pour trouver les documents
            $sql = 'SELECT d.id FROM document d
                    JOIN released_package p ON d.rel_pack_id = p.id
                    WHERE p.id = (SELECT id FROM released_package WHERE id = ? LIMIT 1)';

            try {
                $documentData = $this->em->getConnection()->fetchAllAssociative($sql, [$packageNum]);

                if (empty($documentData)) {
                    $output->writeln("<comment>Package $packageNum → Aucun document trouvé</comment>");
                    continue;
                }

                // Récupérer les entités Document
                $documentIds = array_column($documentData, 'id');
                $documents = $this->em->getRepository(Document::class)->findBy(['id' => $documentIds]);

                if (empty($documents)) {
                    $output->writeln("<comment>Package $packageNum → Aucun document</comment>");
                    continue;
                }
            } catch (\Exception $e) {
                $output->writeln("<error>Erreur lors de la récupération des documents pour le package $packageNum: " . $e->getMessage() . "</error>");
                continue;
            }

            // Vérifier les visas legacy disponibles
            $hasCreationVisa = !empty($legacyData['Creation_VISA']);
            $hasVisaBE2 = !empty($legacyData['VISA_BE_2']);
            $hasVisaBE3 = !empty($legacyData['VISA_BE_3']);

            $visasToApply = [];

            // Déterminer quels visas appliquer selon la progression legacy
            if ($hasCreationVisa || $hasVisaBE2 || $hasVisaBE3) {
                // Toujours commencer par visa_be_0
                $visasToApply[] = [
                    'name' => 'visa_be_0',
                    'date' => $legacyData['Creation_Date'] ?? $legacyData['Reservation_Date'] ?? null
                ];

                if ($hasVisaBE2 || $hasVisaBE3) {
                    $visasToApply[] = [
                        'name' => 'visa_be_1',
                        'date' => $legacyData['DATE_BE_2'] ?? null
                    ];
                }

                if ($hasVisaBE3) {
                    $visasToApply[] = [
                        'name' => 'visa_be',
                        'date' => $legacyData['DATE_BE_3'] ?? null
                    ];
                }
            }

            // Appliquer les visas à tous les documents du package
            if (!empty($visasToApply)) {
                foreach ($documents as $document) {
                    foreach ($visasToApply as $visaData) {
                        $this->createDocumentVisa($document, $visaData['name'], $visaData['date']);
                    }
                    $processedDocuments++;
                }

                $visaNames = array_column($visasToApply, 'name');
                $output->writeln("<comment>Package $packageNum → " . implode(' + ', $visaNames) . " appliqués à " . count($documents) . " documents</comment>");
            } else {
                $output->writeln("<comment>Package $packageNum → Aucun visa (état initial)</comment>");
            }

            $processedPackages++;

            // Flush périodique
            if ($processedPackages % 50 === 0) {
                $this->em->flush();
                $output->writeln("<info>Flush visas packages: $processedPackages packages, $processedDocuments documents traités</info>");
            }
        }

        $output->writeln("<info>Visas de packages appliqués: $processedPackages packages, $processedDocuments documents traités</info>");
    }

    private function createDocumentVisa(Document $document, string $visaName, ?string $dateString): void
    {
        // Vérifier si le visa existe déjà pour éviter les doublons
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $visaName) {
                return; // Visa déjà présent
            }
        }

        $visa = new \App\Entity\Visa();
        $visa->setName($visaName);
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($document);

        // Définir la date du visa
        if (!empty($dateString) && $this->isValidDate($dateString)) {
            $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $dateString) ?: new \DateTimeImmutable($dateString));
        } else {
            // Fallback sur maintenant
            $visa->setDateVisa(new \DateTimeImmutable());
        }

        // Ajouter un validator (utiliser Admin en fallback)
        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
    }

    private $packageVisaCache = []; // Cache pour éviter les requêtes répétées

    private function migratePackageVisasForDocument(Document $document, array $legacyData, OutputInterface $output): void
    {
        // Récupérer le numéro de package legacy
        $packageNum = $legacyData['Rel_Pack_Num'] ?? null;
        if (!$packageNum) {
            return; // Pas de package, rien à faire
        }

        // Vérifier le cache d'abord
        if (!isset($this->packageVisaCache[$packageNum])) {
            try {
                // Récupérer les visas du package legacy
                $packageData = $this->oldDb->fetchAssociative(
                    'SELECT Creation_VISA, VISA_BE_2, VISA_BE_3, Creation_Date, DATE_BE_2, DATE_BE_3 FROM tbl_released_package WHERE Rel_Pack_Num = ?',
                    [$packageNum]
                );

                if (!$packageData) {
                    $this->packageVisaCache[$packageNum] = null; // Marquer comme non trouvé
                    return;
                }

                $this->packageVisaCache[$packageNum] = $packageData;
            } catch (\Exception $e) {
                $reference = $legacyData['Reference'] ?? 'Unknown';
                $output->writeln("<error>Erreur lors de la récupération du package $packageNum pour $reference: " . $e->getMessage() . "</error>");
                return;
            }
        }

        $packageData = $this->packageVisaCache[$packageNum];
        if (!$packageData) {
            return; // Package non trouvé (depuis le cache)
        }

            // Appliquer les visas du package selon la progression legacy
            $hasCreationVisa = !empty($packageData['Creation_VISA']);
            $hasVisaBE2 = !empty($packageData['VISA_BE_2']);
            $hasVisaBE3 = !empty($packageData['VISA_BE_3']);

            // Créer les visas BE selon la progression du package (noms corrects)
            if ($hasCreationVisa) {
                // Toujours commencer par visa_BE_0
                $this->createDocumentVisaIfNotExists($document, 'visa_BE_0', $packageData['Creation_Date'] ?? null);

                if ($hasCreationVisa & $hasVisaBE2 ) {
                    $this->createDocumentVisaIfNotExists($document, 'visa_BE_1', $packageData['DATE_BE_2'] ?? null);
                }

                if ($hasCreationVisa && $hasVisaBE2 && $hasVisaBE3) {
                    $this->createDocumentVisaIfNotExists($document, 'visa_BE', $packageData['DATE_BE_3'] ?? null);
                }

                $reference = $legacyData['Reference'] ?? 'Unknown';
                $visaCount = ($hasCreationVisa ? 1 : 0) + ($hasVisaBE2 ? 1 : 0) + ($hasVisaBE3 ? 1 : 0);
                $output->writeln("<comment>Document $reference → $visaCount visa(s) de package appliqué(s)</comment>");
            }
    }

    private function createDocumentVisaIfNotExists(Document $document, string $visaName, ?string $dateString): void
    {
        // Vérifier si le visa existe déjà pour éviter les doublons
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $visaName) {
                return; // Visa déjà présent
            }
        }

        $visa = new \App\Entity\Visa();
        $visa->setName($visaName);
        $visa->setStatus('valid');
        $visa->setReleasedDrawing($document);

        // Définir la date du visa
        if (!empty($dateString) && $this->isValidDate($dateString)) {
            $visa->setDateVisa(\DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $dateString) ?: new \DateTimeImmutable($dateString));
        } else {
            // Fallback sur maintenant
            $visa->setDateVisa(new \DateTimeImmutable());
        }

        // Ajouter un validator (utiliser Admin en fallback)
        if ($this->adminUser) {
            $visa->setValidator($this->adminUser);
        }

        $this->em->persist($visa);
    }

    private function normalizeName(string $s): string
{
    // 1) trim + retirer tout ce qui n'est pas lettre/chiffre/espace
    $s = trim($s);
    $s = preg_replace('/[^\p{L}\p{N}\s]/u', '', $s);

    // 2) décomposer les accents (NFD) et supprimer les marques
    if (class_exists(\Normalizer::class)) {
        $s = \Normalizer::normalize($s, \Normalizer::FORM_D);
        $s = preg_replace('/\p{M}/u', '', $s);
    }

    // 3) retourner en majuscules
    return mb_strtoupper($s, 'UTF-8');
}


}












// **Package States (currently correct):**
// - Updates: 24 documents ✅
// - Verif: 4 documents ✅  
// - Valid: 2 documents ✅

// **Individual Document States (need correction):**
// - Product Management: 23 documents
// - Inventory: 1 document
// - Quality: 6 documents
// - Project: 5 documents
// - Metrologie: 1 document
// - Quality Prod: 19 documents
// - Assembly: 6 documents
// - Assy. Method: 34 documents
// - Machining: 10 documents
// - Molding: 0 documents
// - RFQ: 8 documents
// - Pris dans: 0 documents
// - FIA: 3 documents
// - ROHS REACH: 828 documents
// - HTS: 933 documents
// - Logistics: 15 documents
// - Assy. Routings: 2 documents
// - SAP Core Data: 25 documents
// - SAP Core Prod: 9 documents
// - Laboratory: 0 documents
