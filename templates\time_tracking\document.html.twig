{% extends 'base.html.twig' %}

{% block title %}D<PERSON><PERSON> du suivi de temps - {{ document.reference }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        border: none;
        margin-bottom: 20px;
    }

    .card-header {
        border-radius: 8px 8px 0 0 !important;
        font-weight: 600;
    }

    .badge {
        font-size: 0.85rem;
        padding: 0.4em 0.6em;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        font-size: 1.1rem;
    }

    .accordion-button:not(.collapsed) {
        background-color: #e7f1ff;
        color: #0d6efd;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(0, 0, 0, 0.125);
    }

    .accordion-item {
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid rgba(0, 0, 0, 0.125);
        overflow: hidden;
    }

    .table th {
        background-color: #f1f5f9;
        font-weight: 600;
    }

    .timeline-container {
        position: relative;
        padding-left: 40px;
    }

    .timeline-container::before {
        content: '';
        position: absolute;
        left: 20px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -30px;
        top: 10px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #0d6efd;
    }

    .timeline-item.exit::before {
        background-color: #dc3545;
    }

    .timeline-date {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .current-state {
        border-left: 4px solid #0d6efd;
    }
</style>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-clock text-primary me-2"></i>
            Détail du suivi de temps
        </h1>
        <a href="{{ path('app_time_tracking') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour à la liste
        </a>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-file-alt me-2"></i>
                Informations du document
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="info-label">Référence</div>
                        <div class="info-value">{{ document.reference }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="info-label">Révision</div>
                        <div class="info-value">{{ document.refRev }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="info-label">Titre</div>
                        <div class="info-value">{{ document.refTitleFra }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="info-label">Jours depuis sortie de BE</div>
                        <div class="info-value">
                            {% if daysSinceBE is not null %}
                                <span class="badge bg-info">{{ daysSinceBE }} jour(s)</span>
                            {% else %}
                                <span class="badge bg-secondary">N/A</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="info-label">Étapes actuelles</div>
                    <div class="info-value mt-2">
                        {% for step, value in document.currentSteps %}
                            <span class="badge bg-primary me-2">{{ step }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Historique des temps par état
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="stateAccordion">
                        {% for state, entries in stateData %}
                            <div class="accordion-item {% if state in document.currentSteps|keys %}current-state{% endif %}">
                                <h2 class="accordion-header" id="heading{{ state|replace({' ': '_', '-': '_'}) }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse{{ state|replace({' ': '_', '-': '_'}) }}"
                                            aria-expanded="false" aria-controls="collapse{{ state|replace({' ': '_', '-': '_'}) }}">
                                        <div class="d-flex justify-content-between w-100 align-items-center">
                                            <span><strong>{{ state }}</strong></span>
                                            <span class="badge {% if state in document.currentSteps|keys %}bg-success{% else %}bg-secondary{% endif %} ms-2">
                                                {{ entries|length }} passage(s)
                                            </span>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse{{ state|replace({' ': '_', '-': '_'}) }}" class="accordion-collapse collapse"
                                     aria-labelledby="heading{{ state|replace({' ': '_', '-': '_'}) }}"
                                     data-bs-parent="#stateAccordion">
                                    <div class="accordion-body">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Entrée</th>
                                                    <th>Sortie</th>
                                                    <th>Durée</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for entry in entries %}
                                                    <tr {% if entry.exit == 'En cours' %}class="table-success"{% endif %}>
                                                        <td>{{ loop.index }}</td>
                                                        <td>{{ entry.enter }}</td>
                                                        <td>{{ entry.exit }}</td>
                                                        <td>{{ entry.duration }}</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-stream me-2"></i>
                        Chronologie du document
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline-container">
                        {% set allEvents = [] %}
                        {% for state, entries in stateData %}
                            {% for entry in entries %}
                                {% set enterEvent = {
                                    'date': entry.enter,
                                    'type': 'enter',
                                    'state': state,
                                    'sortDate': entry.enter|date('YmdHis')
                                } %}
                                {% set allEvents = allEvents|merge([enterEvent]) %}

                                {% if entry.exit != 'En cours' %}
                                    {% set exitEvent = {
                                        'date': entry.exit,
                                        'type': 'exit',
                                        'state': state,
                                        'sortDate': entry.exit|date('YmdHis')
                                    } %}
                                    {% set allEvents = allEvents|merge([exitEvent]) %}
                                {% endif %}
                            {% endfor %}
                        {% endfor %}

                        {% set sortedEvents = allEvents|sort((a, b) => b.sortDate <=> a.sortDate) %}

                        {% for event in sortedEvents %}
                            <div class="timeline-item {% if event.type == 'exit' %}exit{% endif %}">
                                <div class="timeline-date">{{ event.date }}</div>
                                <div class="timeline-content">
                                    {% if event.type == 'enter' %}
                                        <i class="fas fa-sign-in-alt text-success me-2"></i>
                                        Entrée dans l'état <strong>{{ event.state }}</strong>
                                    {% else %}
                                        <i class="fas fa-sign-out-alt text-danger me-2"></i>
                                        Sortie de l'état <strong>{{ event.state }}</strong>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Ouvrir automatiquement les états actuels
        {% for state in document.currentSteps|keys %}
            $('#collapse{{ state|replace({' ': '_', '-': '_'}) }}').addClass('show');
        {% endfor %}
    });
</script>
{% endblock %}
