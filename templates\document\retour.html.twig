{% extends 'base.html.twig' %}

{% block title %}Retour{% endblock %}

{% block body %}
<style>

.DIV-LINE-CARD {
    position: relative;
    padding: 10px 0 0 0;
}

.timeline {
    border-left: 3px solid rgba(0, 56, 240, 0.44); /* Bleu */
    border-bottom: 3px solid rgba(0, 56, 240, 0.44);
    position: absolute;
    top: 40px;
    left: -20px;
    height: 100%;
    width: 20px;
}

#timeline-1 {
    border-top-left-radius: 10px;
    border-top: 3px solid rgba(0, 56, 240, 0.44);
}

.last-one {
    border-bottom-left-radius: 10px;
}

</style>

<div class="container mt-2">
    <div class="card shadow border p-3">
        <h4 class="mb-2">Retour en arrière</h4>
    
        <div class="column p-3 ps-5">
            <div class="DIV-LINE-CARD">
                <div class="timeline" id="timeline-1" style="display: none"></div>
                <div class="card shadow-sm border">
                    <div class="card-body">
                        <h5 class="card-title">Package</h5>
                        <select class="selectpicker" data-style="btn bg-white border" data-size="10" data-width="100%" data-live-search="true" id="package" title="Package">
                            {% for package in packages|reverse %}
                            <option 
                                value="{{ package.id }}" 
                                data-tokens="{{ package.owner }} {{ package.activity }}" 
                                data-content="<div class='d-flex justify-content-between align-items-center'>
                                                <span style='min-width: 12vw;font-weight: bold;'>{{ package.id }}</span> 
                                                <span style='min-width: 12vw; color: rgba(46, 46, 46, 0.7); font-weight: bold;'>{{ package.activity }}</span> 
                                                <span style='min-width: 12vw; font-style: italic; color: rgb(0, 0, 0);'>{{ package.owner }}</span>
                                                <span style='min-width: 12vw; font-style: italic; color: rgb(100, 100, 100); white-space: nowrap;'>{{ package.documents|length }} documents</span>
                                            </div>">
                                {{ package.id }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <div class="DIV-LINE-CARD" style="display: none" id="document-div">
                <div class="timeline" style="display: none"></div>
                <div class="card shadow-sm border">
                    <div class="card-body">
                        <h5 class="card-title">Document</h5>
                        <select class="selectpicker" data-style="btn bg-white border" data-size="10" data-width="100%" data-live-search="true" id="document" title="Document">
                        </select>
                    </div>
                </div>
            </div>
            <div class="DIV-LINE-CARD" style="display: none" id="position-div">
                <div class="timeline" style="display: none"></div>
                <div class="card shadow-sm border">
                    <div class="card-body">
                        <h5 class="card-title">Position</h5>
                        <select class="selectpicker" data-style="btn bg-white border" data-size="10" data-width="100%" data-live-search="true" id="position" title="Position">
                        </select>
                    </div>
                </div>
            </div>
            <div class="DIV-LINE-CARD to" style="display: none" id="retourner-div">
                <div class=""></div>
                <div class="card shadow-sm border">
                    <div class="card-body">
                        <h5 class="card-title">Retourner en</h5>
                        <select class="selectpicker" data-style="btn bg-white border" data-size="10" data-width="100%" data-live-search="true" id="retourner" multiple title="Retourner en">
                        </select>
                    </div>
                </div>
            </div>
            <div class="DIV-LINE-CARD text-end" id="button-div" style="display: none">
                <button class="btn btn-primary" id="valid-button">Valider</button>
            </div>
        </div>
    </div>
</div>

<script>
$('.selectpicker').selectpicker();

function package_change(){
    $('.timeline').removeClass('last-one');
    if ($('#package').val() != '') {
        fetchDocuments();
        $('#timeline-1').slideDown();
        $('#timeline-1').addClass('last-one');
        $('#document-div').find('.timeline').slideUp();
        $('#position-div').find('.timeline').slideUp();
        $('#document-div').slideDown();
        $('#position-div').slideUp();
        $('#retourner-div').slideUp();
        $('#document').selectpicker('val', '');
        $('#position').selectpicker('val', '');
        $('#retourner').selectpicker('val', '');
    } else {
        $('#timeline-1').slideUp();
        $('#document-div').find('.timeline').slideUp();
        $('#position-div').find('.timeline').slideUp();
        $('#document-div').slideUp();
        $('#position-div').slideUp();
        $('#retourner-div').slideUp();
        $('#document').selectpicker('');
        $('#position').selectpicker('');
        $('#retourner').selectpicker('');
    }
}

function document_change(){
    $('.timeline').removeClass('last-one');
    if ($('#document').val() != '') {
        fetchPositionsRetour();
        $('#document-div').find('.timeline').slideDown();
        $('#document-div').find('.timeline').addClass('last-one');
        $('#position-div').find('.timeline').slideUp();
        $('#position-div').slideDown();
        $('#retourner-div').slideUp();
        $('#position').selectpicker('val', '');
        $('#retourner').selectpicker('val', '');
    } else {
        $('#timeline-1').addClass('last-one');
        $('#document-div').find('.timeline').slideUp();
        $('#position-div').find('.timeline').slideUp();
        $('#document-div').slideDown();
        $('#position-div').slideUp();
        $('#retourner-div').slideUp();
        $('#position').selectpicker('');
        $('#retourner').selectpicker('');
    }
}

function position_change(){
    $('.timeline').removeClass('last-one');
    if ($('#position').val() != '') {
        fillRetourner();
        $('#position-div').find('.timeline').slideDown();
        $('#position-div').find('.timeline').addClass('last-one');
        $('#retourner-div').slideDown();
        $('#retourner').selectpicker('val', '');
        $('#button-div').slideUp();
    } else {
        $('#document-div').find('.timeline').addClass('last-one');
        $('#position-div').find('.timeline').slideUp();
        $('#position-div').slideDown();
        $('#retourner-div').slideUp();
        $('#retourner').selectpicker('');
        $('#button-div').slideUp();
    }
}

function retourner_change(){
    if ($('#retourner').val() != '') {
        $('#button-div').slideDown();
    } else {
        $('#button-div').slideUp();
    }
}

$('#document').change(function(){
    document_change();
});

$('#package').change(function(){
    package_change();
});

$('#position').change(function(){
    position_change();
});

$('#retourner').change(function(){
    retourner_change();
});

function fetchDocuments() {
    var url = "{{ path('get_document_package', {'id': 0}) }}";
    url = url.replace('0', $('#package').val());

    $.ajax({
        url: url,
        type: 'POST',
        success: function(response) {
            var $documentSelect = $('#document');
            $documentSelect.empty();
            $documentSelect.selectpicker('destroy');

            response.forEach(function(doc) {
                var optionContent = `
                    <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                        <!-- Texte à gauche -->
                        <div style="flex-grow: 1; min-width: 30vw;">
                            <strong>${doc.reference}</strong> - ${doc.refTitleFra}
                        </div>

                        <!-- Badges à droite -->
                        <div style="display: flex; gap: 0.5rem;">
                            <span class="badge bg-primary">${doc.docType}</span>
                            <span class="badge bg-success">${doc.Material_Type}</span>
                            <span class="badge bg-warning text-dark">${doc.activity}</span>
                        </div>
                    </div>
                `;


                var newOption = $('<option>', {
                    value: doc.id,
                    'data-content': optionContent
                });

                $documentSelect.append(newOption);
            });

            $documentSelect.selectpicker();
        }
    });
}

function fetchPositionsRetour() {
    var url = "{{ path('backward', {'id': 0}) }}";
    url = url.replace('0', $('#document').val());

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (!response || typeof response !== 'object') {
                console.error('Réponse invalide :', response);
                return;
            }

            var $positionSelect = $('#position');
            $positionSelect.empty();
            $positionSelect.selectpicker('destroy');
            // Ajouter uniquement les catégories (les clés)
            Object.keys(response).forEach(category => {
                var newOption = $('<option>', {
                    value: category,
                    text: category,
                    'data-retours': JSON.stringify(response[category]) // Utilisation de data-* pour stocker les retours sous forme de chaîne JSON
                });
                $positionSelect.append(newOption);
            });

            $positionSelect.selectpicker(); // Rafraîchit correctement le selectpicker
        },
        error: function(xhr, status, error) {
            console.error('Erreur AJAX :', status, error);
        }
    });
}

function fillRetourner() {
    var $retournerSelect = $('#retourner');
    $retournerSelect.empty();
    $retournerSelect.selectpicker('destroy');

    var selectedCategory = $('#position').find(':selected');
    var positions = JSON.parse(selectedCategory.attr('data-retours')); // Récupère et parse les retours

    if (Array.isArray(positions)) {
        positions.forEach(function(position) {
            var newOption = $('<option>', {
                value: position, // Assurez-vous que `position` contient la bonne valeur
                text: position   // Si `position` est un objet, utilisez position.name ou similaire
            });
            $retournerSelect.append(newOption);
        });
    }

    $retournerSelect.selectpicker();
}

function send_teleportation(){
    var url = "{{ path('teleportation') }}";
    var data = {
        'document': $('#document').val(),
        'position': $('#position').val(),
        'retourner': $('#retourner').val()
    };

    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        success: function(response) {
            if (response.success) {
                Toast.fire({title: 'Modification réussie !'});
                $('#document').trigger('change');
            } else {
                Toast.fire({title: 'Erreur lors de la modification : ' + response.error});
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur AJAX :', status, error);
        }
    });
}

$('#valid-button').click(function(){
    send_teleportation();
});


</script>
{% endblock %}
