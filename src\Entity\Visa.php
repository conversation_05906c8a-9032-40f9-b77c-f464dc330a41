<?php

namespace App\Entity;

use App\Repository\VisaRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: VisaRepository::class)]
class Visa
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $status = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;


    #[ORM\Column]
    private ?\DateTimeImmutable $dateVisa = null;

    #[ORM\ManyToOne(inversedBy: 'visas')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Document $releasedDrawing = null;

    #[ORM\ManyToOne(inversedBy: 'visas')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $validator = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getDateVisa(): ?\DateTimeImmutable
    {
        return $this->dateVisa;
    }

    public function setDateVisa(\DateTimeImmutable $dateVisa): static
    {
        $this->dateVisa = $dateVisa;

        return $this;
    }

    public function getReleasedDrawing(): ?Document
    {
        return $this->releasedDrawing;
    }

    public function setReleasedDrawing(?Document $releasedDrawing): static
    {
        $this->releasedDrawing = $releasedDrawing;

        return $this;
    }

    public function getValidator(): ?User
    {
        return $this->validator;
    }

    public function setValidator(?User $validator): static
    {
        $this->validator = $validator;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }
    
}
