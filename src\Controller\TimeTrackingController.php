<?php

namespace App\Controller;

use App\Entity\Document;
use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/time-tracking')]
class TimeTrackingController extends AbstractController
{
    #[Route('/navbar-stats', name: 'app_time_tracking_navbar_stats', methods: ['GET'])]
    public function navbarStats(DocumentRepository $documentRepository): Response
    {
        // Utiliser la méthode optimisée avec cache
        $stats = $documentRepository->getNavbarStatsOptimized();

        return $this->render('time_tracking/navbar_stats.html.twig', [
            'totalDocuments' => $stats['totalDocuments'],
            'documentsOutOfBE' => $stats['documentsOutOfBE'],
            'avgDaysSinceBE' => $stats['avgDaysSinceBE'],
            'maxDaysSinceBE' => $stats['maxDaysSinceBE'],
            'documentWithMaxDays' => $stats['documentWithMaxDays'],
        ]);
    }
    #[Route('/', name: 'app_time_tracking', methods: ['GET'])]
    public function index(DocumentRepository $documentRepository): Response
    {
        // Use optimized query to get only documents with state timestamps
        $qb = $documentRepository->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.stateTimestamps IS NOT NULL')
           ->andWhere('d.stateTimestamps != :empty1')
           ->andWhere('d.stateTimestamps != :empty2')
           ->setParameter('empty1', '{}')
           ->setParameter('empty2', '')
           ->orderBy('d.id', 'DESC')
           ->setMaxResults(1000); // Limit for performance

        $documents = $qb->getQuery()->getResult();

        // Préparer les données pour l'affichage
        $timeData = [];
        $stateNames = [];

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            $documentData = [
                'id' => $document->getId(),
                'reference' => $document->getReference(),
                'refRev' => $document->getRefRev(),
                'refTitleFra' => $document->getRefTitleFra(),
                'currentSteps' => array_keys($document->getCurrentSteps()),
                'daysSinceBE' => $document->getDaysSinceBE(),
                'states' => []
            ];

            // Collecter tous les noms d'états pour les en-têtes du tableau
            foreach ($rawTimestamps as $state => $entries) {
                if (!in_array($state, $stateNames)) {
                    $stateNames[] = $state;
                }

                $totalDays = $document->getTotalDaysInState($state);
                $currentDays = $document->getDaysInState($state);

                // Déterminer le nombre d'entrées en fonction du format
                $entriesCount = 1; // Par défaut pour l'ancien format
                if (is_array($entries)) {
                    $entriesCount = count($entries);
                }

                $documentData['states'][$state] = [
                    'totalDays' => $totalDays,
                    'currentDays' => $currentDays,
                    'entries' => $entriesCount,
                    'isActive' => $currentDays !== null
                ];
            }

            $timeData[] = $documentData;
        }

        // Trier les noms d'états selon l'ordre du workflow
        $workflowOrder = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $sortedStateNames = [];
        foreach ($workflowOrder as $state) {
            if (in_array($state, $stateNames)) {
                $sortedStateNames[] = $state;
            }
        }

        // Ajouter les états qui ne sont pas dans l'ordre prédéfini
        foreach ($stateNames as $state) {
            if (!in_array($state, $sortedStateNames)) {
                $sortedStateNames[] = $state;
            }
        }

        return $this->render('time_tracking/index.html.twig', [
            'timeData' => $timeData,
            'stateNames' => $sortedStateNames,
        ]);
    }

    #[Route('/document/{id}', name: 'app_time_tracking_document', methods: ['GET'])]
    public function document(Document $document): Response
    {
        $rawTimestamps = $document->getRawStateTimestamps();

        if (!$rawTimestamps) {
            $this->addFlash('error', 'Aucune donnée de suivi de temps disponible pour ce document.');
            return $this->redirectToRoute('app_time_tracking');
        }

        $stateData = [];

        foreach ($rawTimestamps as $state => $entries) {
            $stateEntries = [];

            // Gestion de l'ancien format (chaîne de caractères directement)
            if (is_string($entries)) {
                $enterDate = new \DateTime($entries);
                $exitDate = null; // Dans l'ancien format, on ne connaît pas la date de sortie

                $now = new \DateTime();
                $interval = $now->diff($enterDate);
                $duration = $interval->days . ' jour' . ($interval->days > 1 ? 's' : '') . ' (en cours)';

                $stateEntries[] = [
                    'enter' => $enterDate->format('d/m/Y H:i:s'),
                    'exit' => 'En cours',
                    'duration' => $duration
                ];
            }
            // Gestion du nouveau format (tableau d'entrées/sorties)
            else if (is_array($entries)) {
                foreach ($entries as $entry) {
                    $enterDate = new \DateTime($entry['enter']);
                    $exitDate = $entry['exit'] ? new \DateTime($entry['exit']) : null;

                    $duration = null;
                    if ($exitDate) {
                        $interval = $exitDate->diff($enterDate);
                        $duration = $interval->days;
                        if ($duration == 0 && ($interval->h > 0 || $interval->i > 0)) {
                            $duration = '< 1 jour';
                        } else {
                            $duration .= ' jour' . ($duration > 1 ? 's' : '');
                        }
                    } else {
                        $now = new \DateTime();
                        $interval = $now->diff($enterDate);
                        $duration = $interval->days . ' jour' . ($interval->days > 1 ? 's' : '') . ' (en cours)';
                    }

                    $stateEntries[] = [
                        'enter' => $enterDate->format('d/m/Y H:i:s'),
                        'exit' => $exitDate ? $exitDate->format('d/m/Y H:i:s') : 'En cours',
                        'duration' => $duration
                    ];
                }
            }

            $stateData[$state] = $stateEntries;
        }

        // Trier les états selon l'ordre du workflow
        $workflowOrder = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $sortedStateData = [];
        foreach ($workflowOrder as $state) {
            if (isset($stateData[$state])) {
                $sortedStateData[$state] = $stateData[$state];
            }
        }

        // Ajouter les états qui ne sont pas dans l'ordre prédéfini
        foreach ($stateData as $state => $entries) {
            if (!isset($sortedStateData[$state])) {
                $sortedStateData[$state] = $entries;
            }
        }

        return $this->render('time_tracking/document.html.twig', [
            'document' => $document,
            'stateData' => $sortedStateData,
            'daysSinceBE' => $document->getDaysSinceBE()
        ]);
    }
}
