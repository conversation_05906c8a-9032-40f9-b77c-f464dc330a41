<nav class="navbar navbar-expand-lg navbar-modern shadow-sm">
    <div class="container-fluid px-4">
        <!-- Logo et titre -->
        <div class="navbar-brand-wrapper d-flex align-items-center">
            <a class="navbar-brand d-flex align-items-center" href="{{ path('app_home') }}">
                <img src="{{ asset('icon.png') }}" alt="SCM Logo" height="32" class="me-2">
                <span class="brand-text">SCM Portal</span>
            </a>
            {# <div class="module-indicator">
                <span class="module-name">DMO</span>
            </div> #}
        </div>

        <!-- Bouton mobile -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Contenu de la navbar -->
        <div class="collapse navbar-collapse" id="navbarContent">
            <!-- Navigation principale -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {{ app.request.attributes.get('_route') == 'app_dmo_index' ? 'active' : '' }}" href="{{ path('app_dmo_index') }}">
                        <i class="fas fa-list me-2"></i>
                        <span>Liste</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ app.request.attributes.get('_route') == 'app_dmo_new' ? 'active' : '' }}" href="{{ path('app_dmo_new') }}">
                        <i class="fas fa-plus me-2"></i>
                        <span>Nouveau</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ app.request.attributes.get('_route') == 'app_dmo_kpi' ? 'active' : '' }}" href="{{ path('app_dmo_kpi') }}">
                        <i class="fas fa-chart-bar me-2"></i>
                        <span>KPI</span>
                    </a>
                </li>
            </ul>

            <!-- Actions secondaires -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog me-2"></i>
                        <span>Outils</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="{{ path('app_dmo_export_csv_template') }}">
                                <i class="fas fa-download me-2"></i>
                                Export CSV
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="{{ path('app_dmo_pdf', { 'filename': 'PR04-02_B_Gestion_des_modifications.pdf' }) }}" target="_blank">
                                <i class="fas fa-file-pdf me-2"></i>
                                Procédure PR04-02
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Retour au portail -->
                <li class="nav-item">
                    <a class="nav-link portal-link" href="{{ path('app_home') }}" title="Retour au portail">
                        <i class="fas fa-home"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>
<style>
/* Navbar moderne */
.navbar-modern {
    background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 0;
}

/* Brand et logo */
.navbar-brand-wrapper {
    gap: 1rem;
}

.navbar-brand {
    text-decoration: none;
    color: white !important;
    font-weight: 600;
    font-size: 1.1rem;
}

.brand-text {
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Indicateur de module */
.module-indicator {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
}

.module-name {
    color: #fff;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* Navigation links */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-link i {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Bouton mobile */
.navbar-toggler {
    color: white;
    font-size: 1.2rem;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Dropdown */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    color: #374151;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background: rgba(0, 155, 255, 0.1);
    color: #009BFF;
    transform: translateX(2px);
}

.dropdown-item i {
    color: #009BFF;
    width: 16px;
}

/* Lien portail */
.portal-link {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
}

.portal-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Responsive */
@media (max-width: 991.98px) {
    .navbar-brand-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .module-indicator {
        align-self: flex-start;
    }

    .navbar-nav {
        padding-top: 1rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }

    .portal-link {
        width: auto;
        height: auto;
        border-radius: 8px;
        padding: 0.5rem 1rem;
    }
}

/* Animation d'entrée */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.navbar-modern {
    animation: slideIn 0.3s ease-out;
}

/* Effet de survol sur les icônes */
.nav-link i {
    transition: transform 0.2s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}
</style>