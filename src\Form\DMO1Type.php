<?php

namespace App\Form;

use App\Entity\DMO;
use App\Entity\ProductRange;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DMO1Type extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('date_init', null, [
                'widget' => 'single_text',
            ])
            ->add('Description')
            ->add('Project')
            ->add('Decision')
            ->add('status')
            ->add('Ex')
            ->add('Indus_Related')
            ->add('date_end', null, [
                'widget' => 'single_text',
            ])
            ->add('Pr_Number')
            ->add('last_Update_Date', null, [
                'widget' => 'single_text',
            ])
            ->add('Ex_Assessment')
            ->add('Spent_Time')
            ->add('Type')
            ->add('Document')
            ->add('requestor', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'id',
            ])
            ->add('Eng_Owner', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'id',
            ])
            ->add('last_Modificator', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'id',
            ])
            ->add('productRange', EntityType::class, [
                'class' => ProductRange::class,
                'choice_label' => 'id',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => DMO::class,
        ]);
    }
}
