-- Performance optimization indexes for JSON fields
-- This script adds indexes to improve query performance on JSON fields

-- Add indexes for current_steps JSON field
-- These indexes will speed up JSON_EXTRACT queries on workflow steps
CREATE INDEX IF NOT EXISTS idx_current_steps_be_0 ON document ((JSON_EXTRACT(current_steps, '$.BE_0')));
CREATE INDEX IF NOT EXISTS idx_current_steps_be_1 ON document ((JSON_EXTRACT(current_steps, '$.BE_1')));
CREATE INDEX IF NOT EXISTS idx_current_steps_be ON document ((JSON_EXTRACT(current_steps, '$.BE')));
CREATE INDEX IF NOT EXISTS idx_current_steps_produit ON document ((JSON_EXTRACT(current_steps, '$.Produit')));
CREATE INDEX IF NOT EXISTS idx_current_steps_qual_logistique ON document ((JSON_EXTRACT(current_steps, '$.Qual_Logistique')));
CREATE INDEX IF NOT EXISTS idx_current_steps_logistique ON document ((JSON_EXTRACT(current_steps, '$.Logistique')));
CREATE INDEX IF NOT EXISTS idx_current_steps_metro ON document ((JSON_EXTRACT(current_steps, '$.Metro')));
CREATE INDEX IF NOT EXISTS idx_current_steps_quality ON document ((JSON_EXTRACT(current_steps, '$.Quality')));
CREATE INDEX IF NOT EXISTS idx_current_steps_achat_rfq ON document ((JSON_EXTRACT(current_steps, '$.Achat_Rfq')));
CREATE INDEX IF NOT EXISTS idx_current_steps_achat_rohs_reach ON document ((JSON_EXTRACT(current_steps, '$.Achat_RoHs_REACH')));
CREATE INDEX IF NOT EXISTS idx_current_steps_assembly ON document ((JSON_EXTRACT(current_steps, '$.Assembly')));
CREATE INDEX IF NOT EXISTS idx_current_steps_machining ON document ((JSON_EXTRACT(current_steps, '$.Machining')));
CREATE INDEX IF NOT EXISTS idx_current_steps_molding ON document ((JSON_EXTRACT(current_steps, '$.Molding')));
CREATE INDEX IF NOT EXISTS idx_current_steps_methode_assemblage ON document ((JSON_EXTRACT(current_steps, '$.Methode_assemblage')));
CREATE INDEX IF NOT EXISTS idx_current_steps_planning ON document ((JSON_EXTRACT(current_steps, '$.Planning')));
CREATE INDEX IF NOT EXISTS idx_current_steps_core_data ON document ((JSON_EXTRACT(current_steps, '$.Core_Data')));
CREATE INDEX IF NOT EXISTS idx_current_steps_project ON document ((JSON_EXTRACT(current_steps, '$.Project')));
CREATE INDEX IF NOT EXISTS idx_current_steps_achat_f30 ON document ((JSON_EXTRACT(current_steps, '$.Achat_F30')));
CREATE INDEX IF NOT EXISTS idx_current_steps_prod_data ON document ((JSON_EXTRACT(current_steps, '$.Prod_Data')));
CREATE INDEX IF NOT EXISTS idx_current_steps_achat_fia ON document ((JSON_EXTRACT(current_steps, '$.Achat_FIA')));
CREATE INDEX IF NOT EXISTS idx_current_steps_achat_hts ON document ((JSON_EXTRACT(current_steps, '$.Achat_Hts')));
CREATE INDEX IF NOT EXISTS idx_current_steps_saisie_hts ON document ((JSON_EXTRACT(current_steps, '$.Saisie_hts')));
CREATE INDEX IF NOT EXISTS idx_current_steps_costing ON document ((JSON_EXTRACT(current_steps, '$.Costing')));
CREATE INDEX IF NOT EXISTS idx_current_steps_gid ON document ((JSON_EXTRACT(current_steps, '$.GID')));
CREATE INDEX IF NOT EXISTS idx_current_steps_indus ON document ((JSON_EXTRACT(current_steps, '$.Indus')));
CREATE INDEX IF NOT EXISTS idx_current_steps_methode_labo ON document ((JSON_EXTRACT(current_steps, '$.methode_Labo')));
CREATE INDEX IF NOT EXISTS idx_current_steps_qprod ON document ((JSON_EXTRACT(current_steps, '$.QProd')));
CREATE INDEX IF NOT EXISTS idx_current_steps_tirage_plans ON document ((JSON_EXTRACT(current_steps, '$.Tirage_Plans')));

-- Add indexes for state_timestamps JSON field
-- These indexes will speed up queries on timestamp data
CREATE INDEX IF NOT EXISTS idx_state_timestamps_not_null ON document (state_timestamps) WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != '';

-- Add composite indexes for frequently used combinations
CREATE INDEX IF NOT EXISTS idx_document_supervisor_timestamps ON document (superviseur_id, state_timestamps) WHERE state_timestamps IS NOT NULL;

-- Add indexes for visa-related queries
CREATE INDEX IF NOT EXISTS idx_visa_released_drawing_name_status ON visa (released_drawing_id, name, status);
CREATE INDEX IF NOT EXISTS idx_visa_validator_date ON visa (validator_id, date_visa);

-- Add indexes for frequently queried document fields
CREATE INDEX IF NOT EXISTS idx_document_reference_rev ON document (reference, ref_rev);
CREATE INDEX IF NOT EXISTS idx_document_material ON document (material);
CREATE INDEX IF NOT EXISTS idx_document_proc_type ON document (proc_type);
CREATE INDEX IF NOT EXISTS idx_document_doc_type ON document (doc_type);
CREATE INDEX IF NOT EXISTS idx_document_pris_dans1 ON document (pris_dans1);

-- Add composite index for document filtering
CREATE INDEX IF NOT EXISTS idx_document_active_filter ON document (id, current_steps, state_timestamps) WHERE current_steps IS NOT NULL;

-- Performance statistics
-- These queries can be used to monitor the effectiveness of the indexes
-- SELECT COUNT(*) as total_documents FROM document;
-- SELECT COUNT(*) as documents_with_current_steps FROM document WHERE current_steps IS NOT NULL AND current_steps != '{}';
-- SELECT COUNT(*) as documents_with_timestamps FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != '';
-- SHOW INDEX FROM document;
-- SHOW INDEX FROM visa;
