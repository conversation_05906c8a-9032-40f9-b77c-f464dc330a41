<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250306105802 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE released_package_dmo (released_package_id INT NOT NULL, dmo_id INT NOT NULL, INDEX IDX_BB113AC1D9657415 (released_package_id), INDEX IDX_BB113AC1B940A464 (dmo_id), PRIMARY KEY(released_package_id, dmo_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE released_package_dmo ADD CONSTRAINT FK_BB113AC1D9657415 FOREIGN KEY (released_package_id) REFERENCES released_package (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE released_package_dmo ADD CONSTRAINT FK_BB113AC1B940A464 FOREIGN KEY (dmo_id) REFERENCES dmo (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE released_package_dmo DROP FOREIGN KEY FK_BB113AC1D9657415');
        $this->addSql('ALTER TABLE released_package_dmo DROP FOREIGN KEY FK_BB113AC1B940A464');
        $this->addSql('DROP TABLE released_package_dmo');
    }
}
