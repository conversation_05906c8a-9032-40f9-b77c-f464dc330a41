{% extends 'base.html.twig' %}

{% block title %}
    Création d'un DMO
{% endblock %}

{% block body %}
    <div class="container mt-5">
        <h1 class="text-center mb-4">Créer un nouveau DMO</h1>

        <form method="POST" action="{{ path('app_dmo_create') }}" enctype="multipart/form-data" class="shadow p-4 rounded bg-light">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="type" class="form-label"><strong>Type de modification</strong></label>
                    <select id="type" name="type" class="form-select">
                        <option value="">-- Sélectionner un type --</option>
                        <option value="Engineering">Engineering</option>
                        <option value="Method Assy.">Assembly Method</option>
                        <option value="Method Lab.">Laboratory Method</option>
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="document" class="form-label"><strong>Type de document</strong></label>
                    <select id="document" name="document" class="form-select">
                        <option value="">← Sélectionner un type de modification --</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="division" class="form-label"><strong>Division</strong></label>
                    <select id="division" name="division" class="form-select">
                        <option value="">-- Sélectionner une division --</option>
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="productRange" class="form-label"><strong>Product Range</strong></label>
                    <select id="productRange" name="productRange" class="form-select">
                        <option value="">← Sélectionner une division  --</option>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label for="project" class="form-label"><strong>Projet</strong></label>
                <input 
                    type="text" 
                    id="project" 
                    name="project" 
                    class="form-control" 
                    placeholder="ex: STAND (ou OTP1234, etc.)"
                >
            </div>

            <div class="mb-3">
                <label for="mesfichiers" class="form-label"><strong>Pièces jointes (10Mo max)</strong></label>
                <input 
                    type="file" 
                    id="mesfichiers" 
                    name="mesfichiers[]" 
                    multiple
                    class="form-control"
                >
                <small class="text-muted">Vous pouvez sélectionner plusieurs fichiers à la fois.</small>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label"><strong>Description *</strong></label>
                <textarea 
                    id="description" 
                    name="description" 
                    rows="5" 
                    class="form-control" 
                    required
                ></textarea>
            </div>

            <button type="submit" class="btn btn-primary w-100">Créer DMO</button>
        </form>
    </div>

    <script>
        $(document).on('change', '#type', function() {
            var type = $(this).val();
            console.log('Type:', type);

            var documentSelect = $("#document");
            documentSelect.html('<option value="">-- Sélectionner un document --</option>');

            if (type === 'Engineering') {
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="Drawing">Drawing</option>');
                documentSelect.append('<option value="NT">NT</option>');
                documentSelect.append('<option value="NU">NU</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else if (type === 'Method Assy.') {
                documentSelect.append('<option value="Assembly Tool">Assembly Tool</option>');
                documentSelect.append('<option value="Assy. Checklist">Assy. Checklist</option>');
                documentSelect.append('<option value="Bonne Prat.">Bonne Prat.</option>');
                documentSelect.append('<option value="COSIR">COSIR</option>');
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="FI">FI</option>');
                documentSelect.append('<option value="FUM">FUM</option>');
                documentSelect.append('<option value="IRS">IRS</option>');
                documentSelect.append('<option value="NU">NU</option>');
                documentSelect.append('<option value="OSIR">OSIR</option>');
                documentSelect.append('<option value="PHI">PHI</option>');
                documentSelect.append('<option value="SAP Drawing">SAP Drawing</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else if (type === 'Method Lab.') {
                documentSelect.append('<option value="Bonne Prat.">Bonne Prat.</option>');
                documentSelect.append('<option value="DEO">DEO</option>');
                documentSelect.append('<option value="FATP">FATP</option>');
                documentSelect.append('<option value="FI">FI</option>');
                documentSelect.append('<option value="FOL">FOL</option>');
                documentSelect.append('<option value="FUM">FUM</option>');
                documentSelect.append('<option value="Lab. Tool">Lab. Tool</option>');
                documentSelect.append('<option value="QPP">QPP</option>');
                documentSelect.append('<option value="QPR">QPR</option>');
                documentSelect.append('<option value="Security Chklst">Security Chklst</option>');
                documentSelect.append('<option value="Specification">Specification</option>');
                documentSelect.append('<option value="Other">Other</option>');
            } else {
                documentSelect.html('<option value="">-- Sélectionner un type de modification ▲ --</option>');
            }
        });

        $(document).ready(function() {
            $.ajax({
                url: "{{ path('app_dmo_getProductRange') }}", // Endpoint qui renvoie { division1: [range1, range2], division2: [...], ... }
                method: 'GET',
                dataType: 'json'
            }).done(function(data) {
                var divisionSelect = $("#division");
                divisionSelect.html('<option value="">-- Sélectionner une division --</option>');
                
                $.each(data, function(division, productRanges) {
                    divisionSelect.append('<option value="' + division + '">' + division + '</option>');
                });
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("Erreur lors du chargement des divisions:", textStatus, errorThrown);
            });

            $(document).on('change', '#division', function() {
                var selectedDivision = $(this).val();
                var productRangeSelect = $("#productRange");
                productRangeSelect.html('<option value="">-- Sélectionner un product range --</option>');

                if (selectedDivision) {
                    $.ajax({
                        url: "{{ path('app_dmo_getProductRange') }}",
                        method: 'GET',
                        dataType: 'json'
                    }).done(function(data) {
                        if (data[selectedDivision]) {
                            $.each(data[selectedDivision], function(index, item) {
                                productRangeSelect.append('<option value="' + item + '">' + item + '</option>');
                            });
                        }
                    }).fail(function(jqXHR, textStatus, errorThrown) {
                        console.error("Erreur lors du chargement des product ranges:", textStatus, errorThrown);
                    });
                }
            });
        });
    </script>
{% endblock %}