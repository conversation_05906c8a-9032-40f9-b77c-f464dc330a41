<nav id="navBar" class="navbar navbar-expand-lg bg-body-tertiary">
  <div class="container-fluid">
    <a class="navbar-brand p-0 parent_logo" href="{{ path('app_home') }}">
      <img src="{{ asset('icon.png') }}" alt="logo" height="30" class="logo_scm d-inline-block align-text-top p-0">
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" 
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link" href="{{ path('app_projet') }}"><i class="fa-regular fa-clock"></i> Imputation</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{{ path('app_user') }}"><i class="fa-regular fa-user"></i> Gestion Utilisateurs</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{{ path('app_impute_index') }}"><i class="fa-regular fa-file-excel"></i> Extractions</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{{ path('app_impute_analyse') }}"><i class="fa-solid fa-chart-line"></i> Statistiques</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{{ path('app_impute_read') }}"><i class="fa-solid fa-clock-rotate-left"></i> Historique</a>
        </li>
      </ul>
      <!-- Dropdown pour afficher les infos de config -->
      <ul class="navbar-nav ms-auto">
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="configDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            Config
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="configDropdown">
            <li>
              <span class="dropdown-item-text" id="configDisplay"></span>
            </li>
			{% if app.user.titre == "Chef de Projets" or app.user.isManager %}
				<li>
					<hr class="dropdown-divider">
				</li>
				<li>
					<button class="dropdown-item" data-bs-toggle="modal" data-bs-target="#configModal">Modifier la config</button>
				</li>
			{% endif %}
          </ul>
        </li>
      </ul>
    </div>
  </div>
</nav>

<!-- Modal avec le formulaire en dur -->
<div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
       <div class="modal-header">
         <h5 class="modal-title" id="configModalLabel">Modifier la configuration</h5>
         <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
       </div>
       <div class="modal-body">
         <form id="configForm">
           <div class="mb-3">
             <label for="periode" class="form-label">Période (YYYY-MM-DD)</label>
             <input type="text" class="form-control" id="periode" name="periode" required>
           </div>
           <div class="mb-3">
             <label for="dateDeb" class="form-label">Date d'ouverture (YYYY-MM-DD)</label>
             <input type="text" class="form-control" id="dateDeb" name="dateDeb" required>
           </div>
           <div class="mb-3">
             <label for="dateFin" class="form-label">Date de clôture (YYYY-MM-DD)</label>
             <input type="text" class="form-control" id="dateFin" name="dateFin" required>
           </div>
           <button type="submit" class="btn btn-primary">Enregistrer</button>
         </form>
       </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Charger la configuration via AJAX
    fetch('{{ path('app_config') }}')
      .then(response => response.json())
      .then(data => {
        // Mettre à jour l'affichage dans le dropdown
        const configDisplay = document.getElementById('configDisplay');
        configDisplay.innerHTML = `
          <strong>Période :</strong> ${data.periode} <br>
          <strong>Début :</strong> ${data.debut} <br>
          <strong>Fin :</strong> ${data.fin} <br>
          <strong>Jours Ouvrables :</strong> ${data.jours_ouvrables} <br>
          <strong>Année Fiscale :</strong> ${data.annee_fiscale} <br>
          <strong>Date d'Ouverture :</strong> ${data.date_ouverture} <br>
          <strong>Date de Clôture :</strong> ${data.date_cloture}
        `;
        // Remplir les champs du formulaire du modal avec les valeurs existantes
        document.getElementById('periode').value = data.date_ouverture; // À adapter si nécessaire
        document.getElementById('dateDeb').value = data.date_ouverture;
        document.getElementById('dateFin').value = data.date_cloture;
      })
      .catch(error => console.error('Error fetching config:', error));
      
    // Gestion de la soumission du formulaire en AJAX
    document.getElementById('configForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const formData = new FormData(this);
      fetch('{{ path("app_config_edit") }}', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(result => {
        if(result.success) {
          // Fermer le modal et rafraîchir l'affichage
          const configModalEl = document.getElementById('configModal');
          const modalInstance = bootstrap.Modal.getInstance(configModalEl);
          modalInstance.hide();
          location.reload(); // ou mettre à jour le dropdown via AJAX
        } else {
          alert('Erreur lors de la sauvegarde');
        }
      })
      .catch(error => console.error('Error submitting form:', error));
    });
  });
</script>

<style>
  /* Réduction de l'espacement vertical dans le dropdown */
  .dropdown-menu {
    padding: 0.25rem 0;
  }
  
  .dropdown-menu > li > a.dropdown-item,
  .dropdown-menu > li > button.dropdown-item,
  .dropdown-menu > li > span.dropdown-item-text {
    padding-top: 0.3rem;
    padding-bottom: 0.3rem;
    margin: 0; /* Supprime d'éventuels marges par défaut */
    font-size: 0.9rem; /* Optionnel : ajustement de la taille de la police */
  }
  
  /* Optionnel : réduction de l'espacement dans le texte du dropdown */
  .dropdown-item-text {
    white-space: pre-line;
    line-height: 1.2; /* Réduit l'interligne pour un rendu plus compact */
  }
  
  .dropdown-item-text strong {
    display: inline-block;
    width: 150px; /* Ajustez selon vos besoins */
  }
  
  /* Ajustements généraux pour la navbar si besoin */
  .navbar-nav .nav-link {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
</style>
