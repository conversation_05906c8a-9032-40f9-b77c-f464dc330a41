<?php

namespace App\Entity;

use App\Repository\ProjectRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProjectRepository::class)]
class Project
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $OTP = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Title = null;

    #[ORM\ManyToOne(inversedBy: 'projects')]
    private ?User $ProjectManager = null;

    /**
     * @var Collection<int, ReleasedPackage>
     */
    #[ORM\OneToMany(targetEntity: ReleasedPackage::class, mappedBy: 'project_relation')]
    private Collection $packages;

    /**
     * @var Collection<int, DMO>
     */
    #[ORM\OneToMany(targetEntity: DMO::class, mappedBy: 'project_relation')]
    private Collection $dmo;

    /**
     * @var Collection<int, Phase>
     */
    #[ORM\OneToMany(targetEntity: Phase::class, mappedBy: 'projet', orphanRemoval: true)]
    private Collection $phases;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $status = null;

    public function __construct()
    {
        $this->packages = new ArrayCollection();
        $this->dmo = new ArrayCollection();
        $this->phases = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOTP(): ?string
    {
        return $this->OTP;
    }

    public function setOTP(?string $OTP): static
    {
        $this->OTP = $OTP;

        return $this;
    }


    public function getTitle(): ?string
    {
        return $this->Title;
    }

    public function setTitle(?string $Title): static
    {
        $this->Title = $Title;

        return $this;
    }

    public function getProjectManager(): ?User
    {
        return $this->ProjectManager;
    }

    public function setProjectManager(?User $ProjectManager): static
    {
        $this->ProjectManager = $ProjectManager;

        return $this;
    }

    /**
     * @return Collection<int, ReleasedPackage>
     */
    public function getPackages(): Collection
    {
        return $this->packages;
    }

    public function addPackage(ReleasedPackage $package): static
    {
        if (!$this->packages->contains($package)) {
            $this->packages->add($package);
            $package->setProjectRelation($this);
        }

        return $this;
    }

    public function removePackage(ReleasedPackage $package): static
    {
        if ($this->packages->removeElement($package)) {
            // set the owning side to null (unless already changed)
            if ($package->getProjectRelation() === $this) {
                $package->setProjectRelation(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getDmo(): Collection
    {
        return $this->dmo;
    }

    public function addDmo(DMO $dmo): static
    {
        if (!$this->dmo->contains($dmo)) {
            $this->dmo->add($dmo);
            $dmo->setProjectRelation($this);
        }

        return $this;
    }

    public function removeDmo(DMO $dmo): static
    {
        if ($this->dmo->removeElement($dmo)) {
            // set the owning side to null (unless already changed)
            if ($dmo->getProjectRelation() === $this) {
                $dmo->setProjectRelation(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Phase>
     */
    public function getPhases(): Collection
    {
        return $this->phases;
    }

    public function addPhase(Phase $phase): static
    {
        if (!$this->phases->contains($phase)) {
            $this->phases->add($phase);
            $phase->setProjet($this);
        }

        return $this;
    }

    public function removePhase(Phase $phase): static
    {
        if ($this->phases->removeElement($phase)) {
            if ($phase->getProjet() === $this) {
                $phase->setProjet(null);
            }
        }

        return $this;
    }

    public function getOpenPhases(): Collection
    {
        return $this->phases->filter(fn(Phase $phase) => $phase->isStatus() && ($phase->isStatusManuel() === null || $phase->isStatusManuel()));
    }

    public function getClosedPhases(): Collection
    {
        return $this->phases->filter(fn(Phase $phase) => !$phase->isStatus() || ($phase->isStatusManuel() !== null && !$phase->isStatusManuel()));
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'OTP' => $this->OTP,
            'Title' => $this->Title,
            'ProjectManager' => $this->ProjectManager ? $this->ProjectManager->toArray() : null,
            'phases' => $this->phases->map(fn(Phase $phase) => $phase->toArray())->toArray(),
            'openPhases' => $this->getOpenPhases()->map(fn(Phase $phase) => $phase->toArray())->toArray(),
            'closedPhases' => $this->getClosedPhases()->map(fn(Phase $phase) => $phase->toArray())->toArray(),
        ];
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function __toString(): string
    {
        return $this->OTP ?? 'STAND';
    }
}
