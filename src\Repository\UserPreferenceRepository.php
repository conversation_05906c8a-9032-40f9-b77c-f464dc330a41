<?php

namespace App\Repository;

use App\Entity\User;
use App\Entity\UserPreference;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserPreference>
 */
class UserPreferenceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserPreference::class);
    }

    public function save(UserPreference $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserPreference $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve une préférence utilisateur par clé
     */
    public function findOneByKey(User $user, string $key): ?UserPreference
    {
        return $this->createQueryBuilder('up')
            ->andWhere('up.user = :user')
            ->andWhere('up.preferenceKey = :key')
            ->setParameter('user', $user)
            ->setParameter('key', $key)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Trouve toutes les préférences d'un utilisateur
     */
    public function findAllByUser(User $user): array
    {
        return $this->createQueryBuilder('up')
            ->andWhere('up.user = :user')
            ->setParameter('user', $user)
            ->getQuery()
            ->getResult();
    }
}
