# Modifications du filtrage des documents pour le dashboard et les analyses

## Résumé des changements

Les modifications implémentent la logique de filtrage demandée pour les documents à risque et les analyses :

### 1. Documents à risque
- **Exclusion des états paniers** : Les documents dans les états `['Achat_Hts', 'QProd', 'methode_Labo', 'Methode_assemblage', 'Achat_RoHs_REACH', 'Tirage_Plans']` ne sont plus considérés comme à risque
- **Exclusion des documents terminés** : Les documents qui ont le visa correspondant à leur état actuel ne sont plus considérés comme à risque

### 2. Analyses
- **Documents terminés** : Un document est considéré comme terminé s'il :
  - A un `current_steps` avec un état ET a le visa correspondant
  - OU est dans un état panier

## Fichiers modifiés

### 1. `src/Utils/DocumentConstants.php` (nouveau)
- Classe utilitaire contenant les constantes partagées
- Définit `PANIER_STATES` pour éviter la duplication de code

### 2. `src/Service/DataAnalysisService.php`
- **Nouvelles méthodes** :
  - `isDocumentCompletedForState()` : Vérifie si un document est terminé pour un état donné
  - `isDocumentCompleted()` : Vérifie si un document est globalement terminé
- **Méthodes modifiées** :
  - `identifyRiskyDocuments()` : Applique les filtres pour exclure les documents terminés et en état panier
  - `analyzeProcessingTimeTrends()` : Ne considère que les documents terminés pour l'analyse
  - `predictProcessingTime()` : Ne considère que les documents terminés pour les prédictions

### 3. `src/Controller/StatisticsController.php`
- **Méthodes modifiées** :
  - `index()` : Sépare les documents actifs et terminés pour des analyses différenciées
  - `documentTypes()` : Utilise les documents terminés pour les temps, actifs pour les états
  - `workflowAnalysis()` : Utilise les documents terminés pour les analyses de performance
- **Nouvelles données** : Ajoute `totalActiveDocuments` et `totalCompletedDocuments` aux templates

### 4. `src/Service/DepartmentPerformanceService.php`
- **Injection de dépendance** : Ajoute `DataAnalysisService` pour utiliser la logique de filtrage
- **Méthode modifiée** :
  - `getDepartmentPerformance()` : Sépare les documents actifs et terminés
  - Calcule les temps de traitement basés sur les documents terminés uniquement
  - Fournit des comptages séparés pour documents actifs/terminés

### 5. `src/Service/DashboardWidgetService.php`
- **Méthode modifiée** :
  - `calculateUrgency()` : Réduit l'urgence des documents dans des états paniers à 5 (très faible)

## Impact sur les analyses

### Avant les modifications :
- Les documents à risque incluaient tous les documents stagnants, même ceux terminés ou en état panier
- Les analyses de temps incluaient des documents non terminés, faussant les statistiques
- Les métriques de performance incluaient des documents en cours de traitement

### Après les modifications :
- **Documents à risque** : Seuls les documents réellement actifs et non terminés sont considérés
- **Analyses de temps** : Basées uniquement sur des documents terminés pour des données précises
- **Métriques de performance** : Séparation claire entre documents actifs et terminés
- **États paniers** : Traités comme des états de finalisation, non prioritaires

## Logique de détermination des documents terminés

Un document est considéré comme terminé si :

1. **État panier** : Le document est dans un des états paniers définis
2. **Visa correspondant** : Le document a le visa correspondant à son état actuel
3. **Cas spécial logistique** : Pour les états `Qual_Logistique`/`Logistique`, les deux visas doivent être présents

## Bénéfices

1. **Précision des analyses** : Les statistiques de temps sont basées sur des données complètes
2. **Pertinence des alertes** : Les documents à risque sont réellement problématiques
3. **Performance** : Réduction du bruit dans les analyses
4. **Cohérence** : Logique uniforme appliquée à travers toute l'application
5. **Flexibilité** : Constantes centralisées pour faciliter les modifications futures
