<?php

namespace App\Command;

use App\Entity\Commentaire;
use App\Entity\Document;
use App\Entity\ReleasedPackage;
use App\Entity\User;
use App\Entity\Visa;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:reset-and-populate-test-data',
    description: 'Vide les tables document, visa, released_package, commentaire et crée des données de test réalistes',
)]
class ResetAndPopulateTestDataCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private UserPasswordHasherInterface $passwordHasher;
    private array $users = [];
    private array $packages = [];
    private array $documents = [];
    private array $places = [
        'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique', 'Metro', 'Quality',
        'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly', 'Machining', 'Molding', 'Methode_assemblage',
        'Planning', 'Core_Data', 'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
        'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd', 'Tirage_Plans'
    ];
    private array $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
    private array $procTypes = ['E', 'F', 'F30', 'F40', 'F50'];
    private array $materialTypes = ['HALB', 'FERT', 'ROH', 'NLAG', 'DIEN', 'HAWA'];
    private array $inventoryImpacts = ['NO IMPACT', 'TO BE SCRAPPED', 'TO BE UPDATED'];
    private array $departments = ['BE', 'Qualité', 'Logistique', 'Méthodes', 'Production', 'Achats', 'Costing', 'GID'];
    private array $commentTypes = ['principal', 'secondaire'];
    private array $commentContents = [
        'Merci de vérifier les dimensions.',
        'Document validé après correction.',
        'Attention aux tolérances.',
        'Vérifier la compatibilité avec les autres composants.',
        'Mise à jour nécessaire suite aux modifications du client.',
        'Besoin de plus d\'informations sur les matériaux.',
        'Validation en attente de confirmation du fournisseur.',
        'Document conforme aux spécifications.',
        'Modifications requises avant validation finale.',
        'Besoin de clarification sur certains points techniques.'
    ];

    public function __construct(EntityManagerInterface $entityManager, UserPasswordHasherInterface $passwordHasher)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->passwordHasher = $passwordHasher;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Réinitialisation et création de données de test');

        // Vider les tables
        $this->clearTables($io);

        // Créer des utilisateurs de test
        $this->createUsers($io);

        // Créer des packages
        $this->createPackages($io);

        // Créer des documents avec des états différents
        $this->createDocuments($io);

        // Créer des visas pour les documents
        $this->createVisas($io);

        // Créer des commentaires
        $this->createCommentaires($io);

        $io->success('Données de test créées avec succès !');

        return Command::SUCCESS;
    }

    private function clearTables(SymfonyStyle $io): void
    {
        $io->section('Nettoyage des tables');

        // Désactiver les contraintes de clé étrangère temporairement
        $this->entityManager->getConnection()->executeStatement('SET FOREIGN_KEY_CHECKS = 0');

        // Vider les tables dans l'ordre pour éviter les problèmes de contraintes
        $this->entityManager->getConnection()->executeStatement('TRUNCATE TABLE commentaire');
        $this->entityManager->getConnection()->executeStatement('TRUNCATE TABLE visa');
        $this->entityManager->getConnection()->executeStatement('TRUNCATE TABLE document');
        $this->entityManager->getConnection()->executeStatement('TRUNCATE TABLE released_package');

        // Réactiver les contraintes de clé étrangère
        $this->entityManager->getConnection()->executeStatement('SET FOREIGN_KEY_CHECKS = 1');

        $io->text('Tables vidées avec succès.');
    }

    private function createUsers(SymfonyStyle $io): void
    {
        $io->section('Création des utilisateurs de test');

        // Vérifier si des utilisateurs existent déjà
        $existingUsers = $this->entityManager->getRepository(User::class)->findAll();
        if (count($existingUsers) > 0) {
            $this->users = $existingUsers;
            $io->text(count($this->users) . ' utilisateurs existants trouvés.');
            return;
        }

        // Créer des utilisateurs pour chaque département
        foreach ($this->departments as $department) {
            // Créer un utilisateur standard
            $user = new User();
            $user->setEmail(strtolower($department) . '@example.com');
            $user->setUsername(strtolower($department) . '_user');
            $user->setNom('Nom_' . $department);
            $user->setPrenom('Prenom_' . $department);
            $user->setRoles(['ROLE_USER']);
            $user->setPassword($this->passwordHasher->hashPassword($user, 'password'));
            $user->setDepartement($department);
            $user->setTitre('Employé ' . $department);
            $user->setIsManager(false);

            $this->entityManager->persist($user);
            $this->users[] = $user;

            // Créer un manager pour ce département
            $manager = new User();
            $manager->setEmail('manager.' . strtolower($department) . '@example.com');
            $manager->setUsername('manager_' . strtolower($department));
            $manager->setNom('Manager_' . $department);
            $manager->setPrenom('Chef_' . $department);
            $manager->setRoles(['ROLE_USER', 'ROLE_MANAGER']);
            $manager->setPassword($this->passwordHasher->hashPassword($manager, 'password'));
            $manager->setDepartement($department);
            $manager->setTitre('Manager ' . $department);
            $manager->setIsManager(true);

            $this->entityManager->persist($manager);
            $this->users[] = $manager;
        }

        // Créer un admin
        $admin = new User();
        $admin->setEmail('<EMAIL>');
        $admin->setUsername('admin');
        $admin->setNom('Admin');
        $admin->setPrenom('Super');
        $admin->setRoles(['ROLE_ADMIN']);
        $admin->setPassword($this->passwordHasher->hashPassword($admin, 'password'));
        $admin->setDepartement('Administration');
        $admin->setTitre('Administrateur');
        $admin->setIsManager(true);

        $this->entityManager->persist($admin);
        $this->users[] = $admin;

        $this->entityManager->flush();
        $io->text(count($this->users) . ' utilisateurs créés.');
    }

    private function createPackages(SymfonyStyle $io): void
    {
        $io->section('Création des packages');

        // Créer 10 packages
        for ($i = 1; $i <= 10; $i++) {
            $package = new ReleasedPackage();
            $package->setDescription('Package de test #' . $i);
            $package->setActivity('Activity ' . rand(1, 5));
            $package->setEx('EX' . rand(100, 999));
            $package->setCreationDate(new \DateTime('-' . rand(1, 60) . ' days'));
            $package->setReservationDate(new \DateTime('-' . rand(1, 30) . ' days'));

            // Assigner un propriétaire, un vérificateur et un validateur aléatoires
            $package->setOwner($this->getRandomUser());
            $package->setVerif($this->getRandomUser());
            $package->setValid($this->getRandomUser());

            $this->entityManager->persist($package);
            $this->packages[] = $package;
        }

        $this->entityManager->flush();
        $io->text(count($this->packages) . ' packages créés.');
    }

    private function createDocuments(SymfonyStyle $io): void
    {
        $io->section('Création des documents');

        // Créer 50 documents
        for ($i = 1; $i <= 50; $i++) {
            $document = new Document();
            $document->setReference('REF' . str_pad($i, 4, '0', STR_PAD_LEFT));
            $document->setRefRev(chr(rand(65, 70))); // A à F
            $document->setRefTitleFra('Document de test #' . $i);
            $document->setProdDraw(rand(0, 1) ? 'GA' . rand(1000, 9999) : 'FT' . rand(1000, 9999));
            $document->setProdDrawRev(chr(rand(65, 70)));
            $document->setAlias('ALIAS' . rand(100, 999));

            // Définir des valeurs aléatoires pour les champs importants
            $docType = $this->docTypes[array_rand($this->docTypes)];
            $document->setDocType($docType);

            $materialType = $this->materialTypes[array_rand($this->materialTypes)];
            $document->setMaterialType($materialType);

            $procType = $this->procTypes[array_rand($this->procTypes)];
            $document->setProcType($procType);

            $inventoryImpact = $this->inventoryImpacts[array_rand($this->inventoryImpacts)];
            $document->setInventoryImpact($inventoryImpact);

            // Assigner à un package aléatoire
            if (!empty($this->packages)) {
                $document->setRelPack($this->packages[array_rand($this->packages)]);
            }

            // Assigner un superviseur aléatoire
            $document->setSuperviseur($this->getRandomUser());

            // Définir des valeurs pour les champs obligatoires selon le WorkflowGuardListener
            $document->setMaterial('MAT' . rand(100, 999));
            $document->setEccn('ECCN' . rand(10, 99));
            $document->setRdo('RDO' . rand(10, 99));
            $document->setHts('HTS' . rand(1000, 9999));
            $document->setDocImpact(rand(0, 1) ? true : false);
            $document->setSwitchAletiq(rand(0, 1) ? true : false);

            // Définir l'état actuel du document
            $currentPlace = $this->places[array_rand($this->places)];
            $document->setCurrentSteps([$currentPlace => 1]);

            // Créer un historique de timestamps pour simuler le passage dans différents états
            $stateTimestamps = $this->generateStateTimestamps($currentPlace);
            $document->setStateTimestamps($stateTimestamps);

            $this->entityManager->persist($document);
            $this->documents[] = $document;
        }

        $this->entityManager->flush();
        $io->text(count($this->documents) . ' documents créés.');
    }

    private function createVisas(SymfonyStyle $io): void
    {
        $io->section('Création des visas');
        $visaCount = 0;

        foreach ($this->documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            $currentPlace = key($currentSteps);

            // Déterminer les visas nécessaires en fonction de l'état actuel
            $requiredVisas = $this->getRequiredVisasForPlace($currentPlace, $document);

            foreach ($requiredVisas as $visaName) {
                $visa = new Visa();
                $visa->setReleasedDrawing($document);
                $visa->setName($visaName);
                $visa->setStatus('valid');
                $visa->setDateVisa(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));
                $visa->setValidator($this->getRandomUser());

                $this->entityManager->persist($visa);
                $visaCount++;
            }
        }

        $this->entityManager->flush();
        $io->text($visaCount . ' visas créés.');
    }

    private function createCommentaires(SymfonyStyle $io): void
    {
        $io->section('Création des commentaires');
        $commentCount = 0;

        foreach ($this->documents as $document) {
            // Créer entre 0 et 5 commentaires par document
            $numComments = rand(0, 5);

            for ($i = 0; $i < $numComments; $i++) {
                $commentaire = new Commentaire();
                $commentaire->setDocuments($document);
                $commentaire->setUser($this->getRandomUser());
                $commentaire->setType($this->commentTypes[array_rand($this->commentTypes)]);
                $commentaire->setCommentaire($this->commentContents[array_rand($this->commentContents)]);
                $commentaire->setState(key($document->getCurrentSteps()));
                $commentaire->setCreatedAt(new \DateTimeImmutable('-' . rand(1, 30) . ' days'));

                $this->entityManager->persist($commentaire);
                $commentCount++;
            }
        }

        $this->entityManager->flush();
        $io->text($commentCount . ' commentaires créés.');
    }

    private function getRandomUser(): User
    {
        return $this->users[array_rand($this->users)];
    }

    private function generateStateTimestamps(string $currentPlace): array
    {
        $timestamps = [];
        $workflowPath = $this->getWorkflowPathTo($currentPlace);

        $now = new \DateTime();
        $daysAgo = count($workflowPath) * 5; // 5 jours par état en moyenne

        foreach ($workflowPath as $index => $place) {
            // Format au nouveau format avec entrées/sorties
            if (!isset($timestamps[$place])) {
                $timestamps[$place] = [];
            }

            // Date d'entrée dans cet état
            $enterDate = (clone $now)->modify('-' . ($daysAgo - ($index * 5)) . ' days');

            // Date de sortie (sauf pour l'état actuel)
            $exitDate = null;
            if ($place !== $currentPlace) {
                $exitDate = (clone $enterDate)->modify('+' . rand(1, 5) . ' days');
            }

            // Ajouter une entrée pour cet état
            $timestamps[$place][] = [
                'enter' => $enterDate->format('Y-m-d H:i:s'),
                'exit' => $exitDate ? $exitDate->format('Y-m-d H:i:s') : null
            ];

            // Simuler un retour dans certains états (20% de chance)
            if ($place !== $currentPlace && rand(1, 5) === 1) {
                $returnEnterDate = (clone $now)->modify('-' . rand(5, 15) . ' days');
                $returnExitDate = (clone $returnEnterDate)->modify('+' . rand(1, 3) . ' days');

                $timestamps[$place][] = [
                    'enter' => $returnEnterDate->format('Y-m-d H:i:s'),
                    'exit' => $returnExitDate->format('Y-m-d H:i:s')
                ];
            }
        }

        return $timestamps;
    }

    private function getWorkflowPathTo(string $targetPlace): array
    {
        // Définir des chemins de workflow réalistes en fonction de l'état cible
        $commonPath = ['BE_0', 'BE_1', 'BE'];

        $specificPaths = [
            'Produit' => array_merge($commonPath, ['Produit']),
            'Qual_Logistique' => array_merge($commonPath, ['Produit', 'Qual_Logistique']),
            'Logistique' => array_merge($commonPath, ['Produit', 'Qual_Logistique', 'Logistique']),
            'Metro' => array_merge($commonPath, ['Produit', 'Metro']),
            'Quality' => array_merge($commonPath, ['Produit', 'Quality']),
            'Achat_Rfq' => array_merge($commonPath, ['Produit', 'Achat_Rfq']),
            'Assembly' => array_merge($commonPath, ['Produit', 'Assembly']),
            'Machining' => array_merge($commonPath, ['Produit', 'Machining']),
            'Molding' => array_merge($commonPath, ['Produit', 'Molding']),
            'Methode_assemblage' => array_merge($commonPath, ['Produit', 'Assembly', 'Methode_assemblage']),
            'Planning' => array_merge($commonPath, ['Produit', 'Planning']),
            'Core_Data' => array_merge($commonPath, ['Produit', 'Core_Data']),
            'Project' => array_merge($commonPath, ['Produit', 'Project']),
            'Achat_F30' => array_merge($commonPath, ['Produit', 'Achat_F30']),
            'Prod_Data' => array_merge($commonPath, ['Produit', 'Prod_Data']),
            'Achat_FIA' => array_merge($commonPath, ['Produit', 'Achat_Rfq', 'Achat_FIA']),
            'Achat_Hts' => array_merge($commonPath, ['Produit', 'Achat_Hts']),
            'Saisie_hts' => array_merge($commonPath, ['Produit', 'Achat_Hts', 'Saisie_hts']),
            'Costing' => array_merge($commonPath, ['Produit', 'Achat_FIA', 'Costing']),
            'GID' => array_merge($commonPath, ['Produit', 'Assembly', 'GID']),
            'Indus' => array_merge($commonPath, ['Produit', 'Assembly', 'Indus']),
            'methode_Labo' => array_merge($commonPath, ['Produit', 'methode_Labo']),
            'QProd' => array_merge($commonPath, ['Produit', 'QProd']),
            'Tirage_Plans' => array_merge($commonPath, ['Produit', 'Tirage_Plans'])
        ];

        return $specificPaths[$targetPlace] ?? $commonPath;
    }

    private function getRequiredVisasForPlace(string $place, Document $document): array
    {
        $visas = [];
        $docType = $document->getDocType();
        $procType = $document->getProcType();

        // Visas communs pour tous les états
        $visas[] = 'visa_BE';

        // Visas spécifiques en fonction de l'état
        switch ($place) {
            case 'Quality':
                $visas[] = 'visa_Quality';
                break;
            case 'Qual_Logistique':
                $visas[] = 'visa_Qual_Logistique';
                break;
            case 'Logistique':
                $visas[] = 'visa_Logistique';
                $visas[] = 'visa_Qual_Logistique';
                break;
            case 'Assembly':
            case 'Machining':
            case 'Molding':
                $visas[] = 'visa_prod';
                break;
            case 'Methode_assemblage':
                $visas[] = 'visa_Methode_assemblage';
                $visas[] = 'visa_prod';
                break;
            case 'Achat_Rfq':
                $visas[] = 'visa_Achat_Rfq';
                break;
            case 'Achat_F30':
                $visas[] = 'visa_Achat_F30';
                break;
            case 'Achat_FIA':
                $visas[] = 'visa_Achat_FIA';
                $visas[] = 'visa_Achat_Rfq';
                break;
            case 'Costing':
                $visas[] = 'visa_Costing';
                if ($docType === 'PUR') {
                    $visas[] = 'visa_Achat_FIA';
                } else {
                    $visas[] = 'visa_GID';
                }
                break;
            case 'GID':
                $visas[] = 'visa_GID';
                if ($docType === 'ASSY') {
                    $visas[] = 'visa_Indus';
                } elseif ($docType === 'MACH' || $docType === 'MOLD') {
                    $visas[] = 'visa_prod';
                }
                break;
            case 'Indus':
                $visas[] = 'visa_Indus';
                if ($docType === 'ASSY') {
                    $visas[] = 'visa_Metro';
                }
                break;
        }

        return $visas;
    }
}
