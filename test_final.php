<?php
/**
 * Test final simple
 */

echo "=== TEST FINAL SIMPLE ===\n\n";

// 1. Vérifier l'environnement
echo "1. ENVIRONNEMENT:\n";
if (file_exists('.env.local')) {
    $content = file_get_contents('.env.local');
    echo "   .env.local:\n";
    echo "   " . str_replace("\n", "\n   ", trim($content)) . "\n";
} else {
    echo "   ❌ .env.local manquant\n";
}

// 2. Test de l'URL count-document
echo "\n2. TEST URL COUNT-DOCUMENT:\n";
$url = 'https://frscmtest.scmlemans.com/index.php/document/count-document';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

echo "   URL: $url\n";
echo "   Code HTTP: $httpCode\n";
echo "   Temps: " . round($totalTime * 1000, 2) . "ms\n";

if ($httpCode === 200) {
    echo "   ✅ Endpoint accessible\n";
    
    // Extraire le JSON de la réponse
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $body = substr($response, strpos($response, "\r\n\r\n") + 4);
    
    $data = json_decode($body, true);
    if ($data) {
        echo "   ✅ JSON valide: " . count($data) . " éléments\n";
        
        // Afficher quelques exemples
        $examples = array_slice($data, 0, 3, true);
        foreach ($examples as $step => $count) {
            echo "      - $step: $count\n";
        }
    } else {
        echo "   ❌ JSON invalide\n";
    }
} else {
    echo "   ❌ Endpoint non accessible\n";
}

// 3. Recommandations
echo "\n3. ACTIONS NÉCESSAIRES:\n";
echo "   🔄 Redémarrer le serveur web (IIS/Apache)\n";
echo "   🧹 Vider le cache navigateur (Ctrl+F5)\n";
echo "   🌐 Tester dans le navigateur\n";

echo "\n=== TEST TERMINÉ ===\n";
