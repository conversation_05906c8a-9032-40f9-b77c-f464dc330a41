# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    default_locale: fr
    translator:
        default_path: '%kernel.project_dir%/translations'
        fallbacks:
            - fr

    #esi: true
    #fragments: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
