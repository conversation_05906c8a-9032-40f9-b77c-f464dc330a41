<?php

namespace App\Repository;

use App\Entity\Document;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends ServiceEntityRepository<Document>
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

//    /**
//     * @return Document[] Returns an array of Document objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Document
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

// findByCurrentStep(string $step)

// public function findByCurrentStepNative(string $key): array
// {
//     // dd($key);
//     $conn = $this->getEntityManager()->getConnection();

//     // Construct the JSON path dynamically
//     $path = '$."' . $key . '"';

//     $sql = "SELECT *
//             FROM document d
//             WHERE JSON_EXTRACT(d.current_steps, :jsonPath) IS NOT NULL";

//     $stmt = $conn->prepare($sql);
//     $stmt->bindValue('jsonPath', $path);
//     $resultSet = $stmt->executeQuery();

//     return $resultSet->fetchAllAssociative();
// }

public function findByCurrentStepNative(string $position)
{
    // Use optimized SQL query instead of loading all documents
    $conn = $this->getEntityManager()->getConnection();

    $sql = "
        SELECT d.*
        FROM document d
        WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
        ORDER BY d.id DESC
    ";

    $result = $conn->executeQuery($sql, ['$."' . $position . '"']);
    $documentsData = $result->fetchAllAssociative();

    // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
    if (empty($documentsData)) {
        return [];
    }

    $documentIds = array_column($documentsData, 'id');

    // Use DQL to load all documents with their relationships in one query
    $qb = $this->createQueryBuilder('d');
    $qb->select('d')
       ->where('d.id IN (:ids)')
       ->setParameter('ids', $documentIds)
       ->orderBy('d.id', 'DESC');

    return $qb->getQuery()->getResult();
}

    public function findLatestByReference(string $reference): ?Document
    {
        // Utiliser une requête DQL plus explicite pour récupérer toutes les propriétés
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
            ->andWhere('d.reference = :reference')
            ->setParameter('reference', $reference)
            ->orderBy('d.refRev', 'DESC')
            ->setMaxResults(1);

        $query = $qb->getQuery();
        $result = $query->getOneOrNullResult();

        if ($result) {
            // Log pour déboguer
            error_log('Document trouvé dans le repository : ' . $result->getReference() . ' - ' . $result->getRefRev());
            error_log('Material : ' . ($result->getMaterial() ?? 'null'));
            error_log('Ex : ' . ($result->getEx() ?? 'null'));
            error_log('MaterialType : ' . ($result->getMaterialType() ?? 'null'));
            error_log('Action : ' . ($result->getAction() ?? 'null'));
            error_log('HTS : ' . ($result->getHts() ?? 'null'));
            error_log('ECCN : ' . ($result->getEccn() ?? 'null'));
            error_log('RDO : ' . ($result->getRdo() ?? 'null'));
            error_log('CustDrawing : ' . ($result->getCustDrawing() ?? 'null'));
        }

        return $result;
    }

    /**
     * Trouve des documents similaires en fonction du type de document, du type de processus et du type de matériau
     */
    public function findSimilarDocuments(?string $docType, ?string $procType, ?string $materialType, int $limit = 20): array
    {
        $qb = $this->createQueryBuilder('d');

        if ($docType) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $docType);
        }

        if ($procType) {
            $qb->andWhere('d.procType = :procType')
               ->setParameter('procType', $procType);
        }

        if ($materialType) {
            $qb->andWhere('d.Material_Type = :materialType')
               ->setParameter('materialType', $materialType);
        }

        return $qb->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Trouve des documents par période - Version optimisée
     */
    public function findByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                -- Check for documents created in the period (old format)
                EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        d.state_timestamps,
                        '$.*' COLUMNS (
                            state_date VARCHAR(255) PATH '$'
                        )
                    ) jt
                    WHERE STR_TO_DATE(jt.state_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
                )
                OR
                -- Check for documents created in the period (new format)
                EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        d.state_timestamps,
                        '$.*[*]' COLUMNS (
                            enter_date VARCHAR(255) PATH '$.enter'
                        )
                    ) jt2
                    WHERE STR_TO_DATE(jt2.enter_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
                )
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s'),
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents à valider par un utilisateur - Version optimisée
     */
    public function findDocumentsToValidateByUser($user): array
    {
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->innerJoin('d.visas', 'v')
           ->innerJoin('v.validator', 'u')
           ->where('u.id = :userId')
           ->andWhere('v.dateVisa IS NULL')
           ->setParameter('userId', $user->getId())
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents à réviser par un utilisateur - Version optimisée
     */
    public function findDocumentsToReviewByUser($user): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.superviseur_id = ?
            AND d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                -- Check for rejection in new format (array entries with from_state containing 'reject')
                JSON_SEARCH(d.state_timestamps, 'one', '%reject%', NULL, '$.*[*].from_state') IS NOT NULL
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [$user->getId()]);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents traités entre deux dates - Version optimisée
     */
    public function findDocumentsProcessedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND EXISTS (
                SELECT 1 FROM JSON_TABLE(
                    d.state_timestamps,
                    '$.*[*]' COLUMNS (
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE jt.exit_date IS NOT NULL
                AND jt.exit_date != ''
                AND STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Compte les documents par étape de workflow de manière optimisée
     * Utilise des requêtes SQL natives pour contourner les limitations de Doctrine avec JSON
     */
    public function countDocumentsByWorkflowStep(): array
    {
        try {
            return $this->countDocumentsByWorkflowStepNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode en cas d'erreur
            return $this->countDocumentsByWorkflowStepFallback();
        }
    }

    /**
     * Version native avec requêtes SQL optimisées
     */
    private function countDocumentsByWorkflowStepNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Liste des étapes de workflow connues
        $workflowSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $counts = [];

        foreach ($workflowSteps as $step) {
            if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                // Cas spécial pour les étapes logistiques (traité une seule fois)
                if (!isset($counts['Qual_Logistique'])) {
                    $sql = "
                        SELECT COUNT(DISTINCT d.id) as count_docs
                        FROM document d
                        WHERE (
                            JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                            OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                        )
                        AND NOT (
                            EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                        )
                    ";

                    $result = $conn->executeQuery($sql);
                    $count = $result->fetchOne();

                    if ($count > 0) {
                        $counts['Qual_Logistique'] = (int)$count;
                        $counts['Logistique'] = (int)$count;
                    }
                }
            } else {
                // Cas général pour les autres étapes
                $sql = "
                    SELECT COUNT(d.id) as count_docs
                    FROM document d
                    WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                    AND NOT EXISTS (
                        SELECT 1 FROM visa v
                        WHERE v.released_drawing_id = d.id
                        AND v.name = ?
                        AND v.status = 'valid'
                    )
                ";

                $result = $conn->executeQuery($sql, [
                    '$."' . $step . '"',
                    'visa_' . $step
                ]);
                $count = $result->fetchOne();

                if ($count > 0) {
                    $counts[$step] = (int)$count;
                }
            }
        }

        return $counts;
    }

    /**
     * Version de fallback utilisant l'ancienne logique mais optimisée
     */
    private function countDocumentsByWorkflowStepFallback(): array
    {
        // Charger tous les documents une seule fois
        $documents = $this->findAll();
        $count = [];

        foreach ($documents as $document) {
            foreach ($document->getCurrentSteps() as $step => $value) {
                if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                    if ($document->hasVisa('visa_Qual_Logistique') && $document->hasVisa('visa_Logistique')) {
                        continue;
                    }
                } else {
                    if ($document->hasVisa('visa_'.$step)) {
                        continue;
                    }
                }

                if (!isset($count[$step])) {
                    $count[$step] = 0;
                }
                $count[$step]++;
            }
        }

        return $count;
    }

    /**
     * Version avec cache du comptage des documents par étape
     * Cache pendant 30 secondes pour éviter les requêtes répétées
     */
    public function countDocumentsByWorkflowStepCached(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Si le cache est vide ou expiré (2 minutes)
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 120) {
            $cache = $this->countDocumentsByWorkflowStep();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Charge les documents avec leurs relations de manière optimisée pour éviter les requêtes N+1
     */
    public function findDocumentsWithRelations(array $documentIds): array
    {
        if (empty($documentIds)) {
            return [];
        }

        $qb = $this->createQueryBuilder('d');
        $qb->select('d', 'v', 'validator', 'rp', 'superviseur', 'c')
           ->leftJoin('d.visas', 'v')
           ->leftJoin('v.validator', 'validator')
           ->leftJoin('d.relPack', 'rp')
           ->leftJoin('d.superviseur', 'superviseur')
           ->leftJoin('d.commentaires', 'c')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Version optimisée pour charger les documents actifs avec toutes leurs relations
     */
    public function findActiveDocumentsInStepWithRelations(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.id
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);

        $documentIds = array_column($result->fetchAllAssociative(), 'id');

        return $this->findDocumentsWithRelations($documentIds);
    }

    /**
     * Trouve les documents actifs dans une étape spécifique (sans visa correspondant)
     * Utilise une requête SQL native pour contourner les limitations de Doctrine avec JSON
     */
    public function findActiveDocumentsInStep(string $step): array
    {
        try {
            return $this->findActiveDocumentsInStepNative($step);
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInStepFallback($step);
        }
    }

    private function findActiveDocumentsInStepNative(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    private function findActiveDocumentsInStepFallback(string $step): array
    {
        $documents = $this->findByCurrentStepNative($step);
        return array_filter($documents, function ($document) use ($step) {
            return !$document->hasVisa('visa_'.$step);
        });
    }

    /**
     * Trouve les documents actifs dans les étapes logistiques
     * (Qual_Logistique ou Logistique sans avoir les deux visas)
     */
    public function findActiveDocumentsInLogisticsSteps(): array
    {
        try {
            return $this->findActiveDocumentsInLogisticsStepsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInLogisticsStepsFallback();
        }
    }

    private function findActiveDocumentsInLogisticsStepsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE (
                JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
            )
            AND NOT (
                EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    private function findActiveDocumentsInLogisticsStepsFallback(): array
    {
        $document0 = $this->findByCurrentStepNative('Qual_Logistique');
        $document1 = $this->findByCurrentStepNative('Logistique');
        $documents = array_unique(array_merge($document0, $document1), SORT_REGULAR);

        return array_filter($documents, function ($document) {
            return !$document->hasVisa('visa_Qual_Logistique') || !$document->hasVisa('visa_Logistique');
        });
    }

    /**
     * Version optimisée de findByCurrentStepNative utilisant une requête SQL native
     */
    public function findByCurrentStepOptimized(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, ['$."' . $step . '"']);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents actifs pour l'analyse de risque - Version optimisée
     */
    public function findActiveDocumentsForRiskAnalysis(int $limit = 50): array
    {
        // Utiliser QueryBuilder au lieu de SQL brut pour éviter les problèmes de paramètres
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.currentSteps IS NOT NULL')
           ->andWhere('d.currentSteps != :empty_json')
           ->andWhere('d.currentSteps != :empty_string')
           ->andWhere('d.stateTimestamps IS NOT NULL')
           ->andWhere('d.stateTimestamps != :empty_json2')
           ->andWhere('d.stateTimestamps != :empty_string2')
           ->setParameter('empty_json', '{}')
           ->setParameter('empty_string', '')
           ->setParameter('empty_json2', '{}')
           ->setParameter('empty_string2', '')
           ->orderBy('d.id', 'DESC')
           ->setMaxResults($limit);

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents terminés dans une période donnée - Version optimisée
     */
    public function findCompletedDocumentsInPeriod(\DateTime $startDate, \DateTime $endDate, ?string $docType = null): array
    {
        // Utiliser QueryBuilder avec jointures pour éviter les problèmes SQL
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->innerJoin('d.visas', 'v_be0', 'WITH', 'v_be0.name = :visa_be0 AND v_be0.status = :valid_status')
           ->innerJoin('d.visas', 'v_costing', 'WITH', 'v_costing.name = :visa_costing AND v_costing.status = :valid_status2')
           ->where('v_be0.dateVisa >= :start_date')
           ->andWhere('v_be0.dateVisa <= :end_date')
           ->setParameter('visa_be0', 'visa_BE_0')
           ->setParameter('visa_costing', 'visa_Costing')
           ->setParameter('valid_status', 'valid')
           ->setParameter('valid_status2', 'valid')
           ->setParameter('start_date', $startDate)
           ->setParameter('end_date', $endDate)
           ->orderBy('d.id', 'DESC');

        if ($docType) {
            $qb->andWhere('d.docType = :doc_type')
               ->setParameter('doc_type', $docType);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Statistiques optimisées pour la navbar avec cache
     */
    public function getNavbarStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 5 minutes pour les stats de navbar
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 300) {
            $cache = $this->calculateNavbarStats();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Statistiques optimisées des documents pour le forecast
     */
    public function getDocumentStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 10 minutes pour les stats globales
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 600) {
            $cache = $this->calculateDocumentStats();
            $cacheTime = $now;
        }

        return $cache;
    }

    private function calculateDocumentStats(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                COUNT(*) as total_count,
                COUNT(CASE WHEN v_costing.id IS NOT NULL THEN 1 END) as completed_count,
                COUNT(CASE WHEN v_costing.id IS NULL THEN 1 END) as active_count
            FROM document d
            LEFT JOIN visa v_costing ON v_costing.released_drawing_id = d.id
                AND v_costing.name = 'visa_Costing'
                AND v_costing.status = 'valid'
        ";

        $result = $conn->executeQuery($sql);
        return $result->fetchAssociative();
    }

    /**
     * Statistiques optimisées par type de document
     */
    public function getDocTypeStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 15 minutes pour les stats par type
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 900) {
            $cache = $this->calculateDocTypeStats();
            $cacheTime = $now;
        }

        return $cache;
    }

    private function calculateDocTypeStats(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
        $stats = [];

        // Initialiser les stats pour tous les types
        foreach ($docTypes as $docType) {
            $stats[$docType] = [
                'count' => 0,
                'avg_time' => 0,
                'total_time' => 0,
            ];
        }

        $sql = "
            SELECT
                d.doc_type,
                COUNT(*) as count,
                AVG(CASE
                    WHEN v_be0.date_visa IS NOT NULL AND v_costing.date_visa IS NOT NULL
                    THEN DATEDIFF(v_costing.date_visa, v_be0.date_visa)
                END) as avg_time,
                SUM(CASE
                    WHEN v_be0.date_visa IS NOT NULL AND v_costing.date_visa IS NOT NULL
                    THEN DATEDIFF(v_costing.date_visa, v_be0.date_visa)
                    ELSE 0
                END) as total_time
            FROM document d
            LEFT JOIN visa v_be0 ON v_be0.released_drawing_id = d.id
                AND v_be0.name = 'visa_BE_0'
                AND v_be0.status = 'valid'
            LEFT JOIN visa v_costing ON v_costing.released_drawing_id = d.id
                AND v_costing.name = 'visa_Costing'
                AND v_costing.status = 'valid'
            WHERE d.doc_type IN ('ASSY', 'MACH', 'MOLD', 'DOC', 'PUR')
            GROUP BY d.doc_type
        ";

        $result = $conn->executeQuery($sql);
        $data = $result->fetchAllAssociative();

        foreach ($data as $row) {
            $docType = $row['doc_type'];
            if (isset($stats[$docType])) {
                $stats[$docType] = [
                    'count' => (int)$row['count'],
                    'avg_time' => round((float)($row['avg_time'] ?? 0), 1),
                    'total_time' => (int)($row['total_time'] ?? 0)
                ];
            }
        }

        return $stats;
    }

    /**
     * Compte le nombre total de documents
     */
    public function countAllDocuments(): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Récupère le nombre de documents par type
     */
    public function getDocumentCountByType(): array
    {
        $result = $this->createQueryBuilder('d')
            ->select('d.docType, COUNT(d.id) as count')
            ->groupBy('d.docType')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach ($result as $row) {
            $counts[$row['docType'] ?? 'Unknown'] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Récupère la distribution des états de workflow
     */
    public function getStateDistribution(): array
    {
        $sql = "
            SELECT
                j.state_name,
                COUNT(*) as count
            FROM document d
            CROSS JOIN (
                SELECT 'BE_0' as state_name UNION ALL
                SELECT 'BE_1' UNION ALL
                SELECT 'BE' UNION ALL
                SELECT 'Core_Data' UNION ALL
                SELECT 'Prod_Data' UNION ALL
                SELECT 'Quality' UNION ALL
                SELECT 'Achat_F30' UNION ALL
                SELECT 'Achat_RFQ' UNION ALL
                SELECT 'Achat_RoHs_REACH' UNION ALL
                SELECT 'Qual_Logistique' UNION ALL
                SELECT 'Project' UNION ALL
                SELECT 'GID' UNION ALL
                SELECT 'Produit'
            ) j
            WHERE JSON_EXTRACT(d.current_steps, CONCAT('$.', j.state_name)) = '1'
            GROUP BY j.state_name
            ORDER BY count DESC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $distribution[$row['state_name']] = (int)$row['count'];
        }

        return $distribution;
    }

    /**
     * Calcule le temps moyen par état
     */
    public function getAverageTimeByState(): array
    {
        // Cette méthode nécessite une logique complexe, pour l'instant on retourne un placeholder
        // TODO: Implémenter le calcul réel basé sur state_timestamps
        return [
            'BE_0' => 2.5,
            'BE_1' => 3.2,
            'BE' => 4.1,
            'Core_Data' => 1.8,
            'Prod_Data' => 2.3,
            'Quality' => 3.5,
            'Achat_F30' => 2.1,
            'Achat_RFQ' => 2.8,
            'Achat_RoHs_REACH' => 1.9,
            'Qual_Logistique' => 2.7,
            'Project' => 3.1,
            'GID' => 1.5,
            'Produit' => 2.9
        ];
    }

    /**
     * Récupère la distribution des documents par département
     */
    public function getDocumentDistributionByDepartment(): array
    {
        $sql = "
            SELECT
                COALESCE(u.departement, 'Unknown') as department,
                COUNT(d.id) as count
            FROM document d
            LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
            LEFT JOIN user u ON rp.owner_id = u.id
            GROUP BY u.departement
            ORDER BY count DESC
            LIMIT 10
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $distribution[$row['department']] = (int)$row['count'];
        }

        return $distribution;
    }

    /**
     * Récupère la distribution des documents par utilisateur
     */
    public function getDocumentDistributionByUser(): array
    {
        $sql = "
            SELECT
                CONCAT(COALESCE(u.prenom, ''), ' ', COALESCE(u.nom, '')) as user_name,
                COUNT(d.id) as count
            FROM document d
            LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
            LEFT JOIN user u ON rp.owner_id = u.id
            WHERE u.id IS NOT NULL
            GROUP BY u.id, u.prenom, u.nom
            ORDER BY count DESC
            LIMIT 10
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $userName = trim($row['user_name']);
            if (!empty($userName)) {
                $distribution[$userName] = (int)$row['count'];
            }
        }

        return $distribution;
    }

    /**
     * Compte les mises à jour d'un utilisateur dans une période donnée
     */
    public function countUserUpdatesInPeriod(int $userId, \DateTime $startDate, \DateTime $endDate): int
    {
        // Simplification pour éviter les problèmes de performance et de compatibilité SQL
        // Pour l'instant, on retourne 0 - cette fonctionnalité peut être implémentée plus tard
        // avec une approche différente si nécessaire
        return 0;
    }

    /**
     * Calcule les statistiques de navbar de manière optimisée
     */
    private function calculateNavbarStats(): array
    {
        try {
            return $this->calculateNavbarStatsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->calculateNavbarStatsFallback();
        }
    }

    /**
     * Version optimisée avec requêtes SQL natives
     */
    private function calculateNavbarStatsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Compter le total de documents
        $totalDocuments = $conn->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();

        // Compter les documents sortis de BE (qui ont des state_timestamps)
        $documentsOutOfBE = $conn->executeQuery("
            SELECT COUNT(*) FROM document
            WHERE state_timestamps IS NOT NULL
            AND state_timestamps != '{}'
            AND state_timestamps != ''
        ")->fetchOne();

        // Calculer les statistiques de temps depuis BE
        $timeStats = $conn->executeQuery("
            SELECT
                AVG(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as avg_days,
                MAX(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as max_days
            FROM document
            WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
        ")->fetchAssociative();

        $avgDaysSinceBE = $timeStats['avg_days'] ? round($timeStats['avg_days'], 1) : 0;
        $maxDaysSinceBE = $timeStats['max_days'] ? (int)$timeStats['max_days'] : 0;

        // Trouver le document avec le maximum de jours
        $documentWithMaxDays = null;
        if ($maxDaysSinceBE > 0) {
            $maxDocId = $conn->executeQuery("
                SELECT id FROM document
                WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
                AND DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter'))) = ?
                LIMIT 1
            ", [$maxDaysSinceBE])->fetchOne();

            if ($maxDocId) {
                $documentWithMaxDays = $this->find($maxDocId);
            }
        }

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => (int)$documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }

    /**
     * Version de fallback utilisant l'ancienne logique
     */
    private function calculateNavbarStatsFallback(): array
    {
        // Utiliser une requête plus simple pour éviter de charger tous les documents
        $totalDocuments = $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Charger seulement les documents avec des timestamps pour les calculs
        $documentsWithTimestamps = $this->createQueryBuilder('d')
            ->where('d.stateTimestamps IS NOT NULL')
            ->andWhere('d.stateTimestamps != :empty1')
            ->andWhere('d.stateTimestamps != :empty2')
            ->setParameter('empty1', '{}')
            ->setParameter('empty2', '')
            ->getQuery()
            ->getResult();

        $documentsOutOfBE = 0;
        $totalDaysSinceBE = 0;
        $maxDaysSinceBE = 0;
        $documentWithMaxDays = null;

        foreach ($documentsWithTimestamps as $document) {
            $daysSinceBE = $document->getDaysSinceBE();
            if ($daysSinceBE !== null) {
                $documentsOutOfBE++;
                $totalDaysSinceBE += $daysSinceBE;

                if ($daysSinceBE > $maxDaysSinceBE) {
                    $maxDaysSinceBE = $daysSinceBE;
                    $documentWithMaxDays = $document;
                }
            }
        }

        $avgDaysSinceBE = $documentsOutOfBE > 0 ? round($totalDaysSinceBE / $documentsOutOfBE, 1) : 0;

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => $documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }

    /**
     * Analyse optimisée des goulots par état avec SQL natif - Version simplifiée
     */
    public function getStateBottleneckAnalysis(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Requête optimisée qui récupère tous les documents terminés avec leurs états et temps de traitement
        $sql = "
            SELECT
                d.id,
                d.current_steps,
                DATEDIFF(v2.date_visa, v1.date_visa) as processing_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE d.current_steps IS NOT NULL
            AND d.current_steps != '{}'
            AND v2.date_visa >= v1.date_visa
            AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
        ";

        $result = $conn->executeQuery($sql);
        $documents = $result->fetchAllAssociative();

        $stateStats = [];

        // Traiter les résultats en PHP pour éviter les requêtes JSON complexes
        foreach ($documents as $doc) {
            $currentSteps = json_decode($doc['current_steps'], true);
            $processingTime = (int)$doc['processing_time'];

            if (is_array($currentSteps)) {
                foreach (array_keys($currentSteps) as $state) {
                    if (!isset($stateStats[$state])) {
                        $stateStats[$state] = [
                            'document_count' => 0,
                            'total_time' => 0,
                            'min_time' => PHP_INT_MAX,
                            'max_time' => 0
                        ];
                    }

                    $stateStats[$state]['document_count']++;
                    $stateStats[$state]['total_time'] += $processingTime;
                    $stateStats[$state]['min_time'] = min($stateStats[$state]['min_time'], $processingTime);
                    $stateStats[$state]['max_time'] = max($stateStats[$state]['max_time'], $processingTime);
                }
            }
        }

        // Corriger les valeurs min_time pour les états qui ont des documents
        foreach ($stateStats as &$stats) {
            if ($stats['min_time'] === PHP_INT_MAX) {
                $stats['min_time'] = 0;
            }
        }

        // Trier par temps total décroissant
        uasort($stateStats, function($a, $b) {
            return $b['total_time'] <=> $a['total_time'];
        });

        return $stateStats;
    }

    /**
     * Récupère les temps de traitement individuels pour un état donné - Version optimisée
     */
    public function getProcessingTimesForState(string $state): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT DATEDIFF(v2.date_visa, v1.date_visa) as processing_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND v2.date_visa >= v1.date_visa
            AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
        ";

        $result = $conn->executeQuery($sql, ['$."' . $state . '"']);
        $data = $result->fetchAllNumeric();

        return array_map('intval', array_column($data, 0));
    }

    /**
     * Analyse optimisée des transitions entre états
     */
    public function getTransitionBottleneckAnalysis(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Cette requête est complexe car elle nécessite d'analyser les timestamps JSON
        // Pour l'instant, on retourne un tableau vide et on peut l'implémenter plus tard si nécessaire
        // L'analyse des transitions nécessite une logique complexe pour parser les state_timestamps

        return [];
    }

    /**
     * Analyse optimisée par type de document
     */
    public function getDocumentTypeBottleneckAnalysis(array $docTypes): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $placeholders = str_repeat('?,', count($docTypes) - 1) . '?';

        $sql = "
            SELECT
                d.doc_type,
                COUNT(*) as total_documents,
                AVG(DATEDIFF(
                    (SELECT v2.date_visa FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid' LIMIT 1),
                    (SELECT v1.date_visa FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid' LIMIT 1)
                )) as avg_processing_time,
                MIN(DATEDIFF(
                    (SELECT v2.date_visa FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid' LIMIT 1),
                    (SELECT v1.date_visa FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid' LIMIT 1)
                )) as min_time,
                MAX(DATEDIFF(
                    (SELECT v2.date_visa FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid' LIMIT 1),
                    (SELECT v1.date_visa FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid' LIMIT 1)
                )) as max_time
            FROM document d
            WHERE d.doc_type IN ($placeholders)
            AND EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid')
            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid')
            GROUP BY d.doc_type
        ";

        $result = $conn->executeQuery($sql, $docTypes);
        $data = $result->fetchAllAssociative();

        $typeStats = [];
        foreach ($data as $row) {
            $typeStats[$row['doc_type']] = [
                'total_documents' => (int)$row['total_documents'],
                'avg_processing_time' => round((float)$row['avg_processing_time'], 1),
                'min_time' => (int)$row['min_time'],
                'max_time' => (int)$row['max_time']
            ];
        }

        return $typeStats;
    }

    /**
     * Analyse temporelle optimisée
     */
    public function getTemporalBottleneckAnalysis(int $monthsBack = 6): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Générer les 6 derniers mois même s'ils n'ont pas de données
        $monthlyAnalysis = [];
        for ($i = $monthsBack - 1; $i >= 0; $i--) {
            $date = new \DateTime();
            $date->modify("-{$i} months");
            $monthKey = $date->format('Y-m');
            $monthlyAnalysis[$monthKey] = [
                'month' => $date->format('M Y'),
                'document_count' => 0,
                'avg_processing_time' => 0
            ];
        }

        $sql = "
            SELECT
                DATE_FORMAT(v1.date_visa, '%Y-%m') as month_key,
                DATE_FORMAT(v1.date_visa, '%M %Y') as month_label,
                COUNT(*) as document_count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_processing_time
            FROM visa v1
            INNER JOIN visa v2 ON v1.released_drawing_id = v2.released_drawing_id
            WHERE v1.name = 'visa_BE_0' AND v1.status = 'valid'
            AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            AND v1.date_visa >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            AND v2.date_visa >= v1.date_visa
            GROUP BY DATE_FORMAT(v1.date_visa, '%Y-%m')
            ORDER BY month_key DESC
        ";

        $result = $conn->executeQuery($sql, [$monthsBack]);
        $data = $result->fetchAllAssociative();

        // Fusionner les données réelles avec la structure par défaut
        foreach ($data as $row) {
            if (isset($monthlyAnalysis[$row['month_key']])) {
                $monthlyAnalysis[$row['month_key']] = [
                    'month' => $row['month_label'],
                    'document_count' => (int)$row['document_count'],
                    'avg_processing_time' => round((float)$row['avg_processing_time'], 1)
                ];
            }
        }

        return $monthlyAnalysis;
    }

    /**
     * Statistiques optimisées pour la page forecast
     */
    public function getForecastDocumentStats(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Compter le total de documents
        $totalSql = "SELECT COUNT(*) as total FROM document";
        $totalResult = $conn->executeQuery($totalSql);
        $totalDocuments = (int)$totalResult->fetchOne();

        // Compter les documents terminés (avec visa BE_0 ET visa Costing)
        $completedSql = "
            SELECT COUNT(DISTINCT d.id) as completed
            FROM document d
            WHERE EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid')
            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid')
        ";
        $completedResult = $conn->executeQuery($completedSql);
        $completedDocuments = (int)$completedResult->fetchOne();

        $activeDocuments = $totalDocuments - $completedDocuments;

        return [
            'total_documents' => $totalDocuments,
            'active_documents' => $activeDocuments,
            'completed_documents' => $completedDocuments
        ];
    }

    /**
     * Analyse optimisée des tendances de temps de traitement
     */
    public function getProcessingTimeTrendsOptimized(string $period = 'month', int $limit = 6, ?string $docType = null): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Définir le format de date selon la période
        $dateFormat = match($period) {
            'week' => '%u/%Y',
            'month' => '%m/%Y',
            'quarter' => 'Q%q/%Y',
            'year' => '%Y',
            default => '%m/%Y'
        };

        // Calculer la date de début selon la période
        $startDate = new \DateTime();
        switch ($period) {
            case 'week':
                $startDate->modify("-{$limit} weeks");
                break;
            case 'month':
                $startDate->modify("-{$limit} months");
                break;
            case 'quarter':
                $startDate->modify("-" . ($limit * 3) . " months");
                break;
            case 'year':
                $startDate->modify("-{$limit} years");
                break;
            default:
                $startDate->modify("-{$limit} months");
        }

        // Construire la requête SQL avec date fixe
        $sql = "
            SELECT
                DATE_FORMAT(v1.date_visa, '$dateFormat') as period_key,
                COUNT(*) as document_count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_processing_time,
                SUM(DATEDIFF(v2.date_visa, v1.date_visa)) as total_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
            AND v1.date_visa >= ?
        ";

        $params = [$startDate->format('Y-m-d')];

        if ($docType) {
            $sql .= " AND d.doc_type = ?";
            $params[] = $docType;
        }

        $sql .= "
            GROUP BY DATE_FORMAT(v1.date_visa, '$dateFormat')
            ORDER BY period_key DESC
            LIMIT $limit
        ";

        $result = $conn->executeQuery($sql, $params);
        $data = $result->fetchAllAssociative();

        // Générer toutes les périodes pour avoir une structure complète
        $trends = [];
        $now = new \DateTime();

        for ($i = $limit - 1; $i >= 0; $i--) {
            $date = clone $now;

            switch ($period) {
                case 'week':
                    $date->modify("-{$i} weeks");
                    $periodKey = $date->format('W/Y');
                    $label = 'Semaine ' . $date->format('W/Y');
                    break;
                case 'month':
                    $date->modify("-{$i} months");
                    $periodKey = $date->format('m/Y');
                    $label = $date->format('m/Y');
                    break;
                case 'quarter':
                    $date->modify("-" . ($i * 3) . " months");
                    $quarter = ceil($date->format('n') / 3);
                    $periodKey = "Q{$quarter}/" . $date->format('Y');
                    $label = "T{$quarter} " . $date->format('Y');
                    break;
                case 'year':
                    $date->modify("-{$i} years");
                    $periodKey = $date->format('Y');
                    $label = $date->format('Y');
                    break;
                default:
                    $date->modify("-{$i} months");
                    $periodKey = $date->format('m/Y');
                    $label = $date->format('m/Y');
            }

            $trends[$periodKey] = [
                'label' => $label,
                'document_count' => 0,
                'avg_processing_time' => 0,
                'total_time' => 0,
                'start_date' => clone $date,
                'end_date' => clone $date
            ];
        }

        // Fusionner les données réelles
        foreach ($data as $row) {
            $periodKey = $row['period_key'];
            if (isset($trends[$periodKey])) {
                $trends[$periodKey]['document_count'] = (int)$row['document_count'];
                $trends[$periodKey]['avg_processing_time'] = round((float)$row['avg_processing_time'], 1);
                $trends[$periodKey]['total_time'] = (int)$row['total_time'];
            }
        }

        return $trends;
    }

    /**
     * Identifie les documents à risque de manière optimisée
     */
    public function getRiskyDocumentsOptimized(int $thresholdDays = 7): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Pour éviter les problèmes de collation, utilisons une approche plus simple
        // Récupérer les documents actifs avec leurs états actuels
        $sql = "
            SELECT
                d.id,
                d.reference,
                d.doc_type,
                d.current_steps,
                d.state_timestamps
            FROM document d
            WHERE d.current_steps IS NOT NULL
            AND d.current_steps != '{}'
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = 'visa_Costing'
                AND v.status = 'valid'
            )
            LIMIT 200
        ";

        $result = $conn->executeQuery($sql);
        $documents = $result->fetchAllAssociative();

        $riskyDocuments = [];
        $panierStates = ['Achat_Hts', 'QProd', 'methode_Labo', 'Methode_assemblage', 'Achat_RoHs_REACH', 'Tirage_Plans'];
        $now = new \DateTime();

        foreach ($documents as $docData) {
            $currentSteps = json_decode($docData['current_steps'], true);
            $stateTimestamps = json_decode($docData['state_timestamps'], true);

            if (!is_array($currentSteps) || !is_array($stateTimestamps)) {
                continue;
            }

            foreach (array_keys($currentSteps) as $state) {
                // Exclure les états paniers
                if (in_array($state, $panierStates)) {
                    continue;
                }

                // Vérifier si le document a le visa pour cet état
                $hasVisa = $conn->executeQuery(
                    "SELECT 1 FROM visa WHERE released_drawing_id = ? AND name = ? AND status = 'valid' LIMIT 1",
                    [$docData['id'], 'visa_' . $state]
                )->fetchOne();

                if ($hasVisa) {
                    continue; // Document terminé pour cet état
                }

                // Calculer les jours dans l'état
                if (isset($stateTimestamps[$state])) {
                    $timestamps = $stateTimestamps[$state];
                    $enterDate = null;

                    if (is_array($timestamps) && !empty($timestamps)) {
                        $lastEntry = end($timestamps);
                        if (isset($lastEntry['enter'])) {
                            $enterDate = new \DateTime($lastEntry['enter']);
                        }
                    } elseif (is_string($timestamps)) {
                        $enterDate = new \DateTime($timestamps);
                    }

                    if ($enterDate) {
                        $daysInState = $now->diff($enterDate)->days;
                        if ($daysInState >= $thresholdDays) {
                            $document = $this->find($docData['id']);
                            if ($document) {
                                $riskyDocuments[] = [
                                    'document' => $document,
                                    'state' => $state,
                                    'days_in_state' => $daysInState,
                                    'enter_date' => $enterDate,
                                ];
                            }
                        }
                    }
                }
            }
        }

        // Trier par nombre de jours décroissant
        usort($riskyDocuments, function($a, $b) {
            return $b['days_in_state'] - $a['days_in_state'];
        });

        return array_slice($riskyDocuments, 0, 50); // Limiter à 50 résultats
    }

    /**
     * Récupère les données pour l'analyse des outliers de manière optimisée
     */
    public function getOutliersAnalysisData(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                d.id,
                d.reference,
                d.doc_type,
                DATEDIFF(v2.date_visa, v1.date_visa) as processing_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
            AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
            ORDER BY processing_time DESC
            LIMIT 500
        ";

        $result = $conn->executeQuery($sql);
        $data = $result->fetchAllAssociative();

        $processingTimes = [];
        foreach ($data as $row) {
            $processingTimes[] = [
                'document_id' => (int)$row['id'],
                'reference' => $row['reference'],
                'doc_type' => $row['doc_type'],
                'time' => (int)$row['processing_time']
            ];
        }

        return $processingTimes;
    }
}
