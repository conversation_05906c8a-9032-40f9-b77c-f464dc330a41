{% extends 'base.html.twig' %}

{% block title %}Analyse du workflow{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        border: none;
        margin-bottom: 20px;
    }

    .card-header {
        border-radius: 8px 8px 0 0 !important;
        font-weight: 600;
    }

    .stats-card {
        transition: transform 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
    }

    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .table th {
        background-color: #f1f5f9;
        font-weight: 600;
    }

    .progress {
        height: 20px;
        border-radius: 10px;
    }

    .workflow-diagram {
        width: 100%;
        height: 500px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .transition-arrow {
        color: #0d6efd;
        margin: 0 5px;
    }

    .efficiency-card {
        border-left: 4px solid;
    }

    .efficiency-card.productive {
        border-left-color: #28a745;
    }

    .efficiency-card.waiting {
        border-left-color: #dc3545;
    }

    .efficiency-card.other {
        border-left-color: #6c757d;
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-project-diagram text-primary me-2"></i>
            Analyse du workflow
        </h1>
        <div>
            <a href="{{ path('app_statistics') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-chart-line"></i> Tableau de bord
            </a>
            <a href="{{ path('app_statistics_document_types') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-file-alt"></i> Types de documents
            </a>
            <a href="{{ path('app_time_tracking') }}" class="btn btn-secondary">
                <i class="fas fa-clock"></i> Suivi des temps
            </a>
        </div>
    </div>

    <!-- Vue d'ensemble -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ averageTransitionsPerDocument }}</div>
                    <div class="stats-label">Transitions moyennes par document</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ workflowEfficiency.average_cycle_time }}</div>
                    <div class="stats-label">Temps de cycle moyen (jours)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ transitionCounts|length }}</div>
                    <div class="stats-label">Chemins uniques</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ workflowEfficiency.completed_documents }}</div>
                    <div class="stats-label">Documents complétés</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagramme du workflow -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>
                        Diagramme du workflow
                    </h5>
                </div>
                <div class="card-body">
                    <div id="workflowDiagram" class="workflow-diagram"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Transitions les plus fréquentes -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        Transitions les plus fréquentes
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Transition</th>
                                <th>Nombre</th>
                                <th>Fréquence</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set totalTransitions = 0 %}
                            {% for transition, count in transitionCounts %}
                                {% set totalTransitions = totalTransitions + count %}
                            {% endfor %}

                            {% for transition, count in transitionCounts|slice(0, 10) %}
                                <tr>
                                    <td>
                                        {% set states = transition|split(' -> ') %}
                                        <span class="badge bg-secondary">{{ states[0] }}</span>
                                        <i class="fas fa-arrow-right transition-arrow"></i>
                                        <span class="badge bg-primary">{{ states[1] }}</span>
                                    </td>
                                    <td>{{ count }}</td>
                                    <td>
                                        <div class="progress">
                                            {% set percentage = (count / totalTransitions) * 100 %}
                                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ percentage }}%">
                                                {{ percentage|round(1) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Répartition du temps par catégorie -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Répartition du temps par catégorie
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container mb-4">
                        <canvas id="timeDistributionChart"></canvas>
                    </div>

                    <div class="row">
                        {% set colors = {
                            'Création': '#0d6efd',
                            'Production': '#fd7e14',
                            'Achats': '#dc3545',
                            'Qualité': '#20c997',
                            'Logistique': '#6f42c1',
                            'Costing': '#ffc107',
                            'Autres': '#6c757d'
                        } %}

                        {% for category, time in workflowEfficiency.time_by_category %}
                            <div class="col-md-3 mb-3">
                                <div class="card efficiency-card" style="border-left: 4px solid {{ colors[category] }};">
                                    <div class="card-body p-3">
                                        <h6 class="card-title">{{ category }}</h6>
                                        <p class="card-text">{{ time }} jours ({{ workflowEfficiency.percentage_by_category[category] }}%)</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Données pour le graphique de répartition du temps
        const timeDistributionData = {
            labels: [
                {% for category, time in workflowEfficiency.time_by_category %}
                    '{{ category }}',
                {% endfor %}
            ],
            datasets: [{
                data: [
                    {% for category, time in workflowEfficiency.time_by_category %}
                        {{ time }},
                    {% endfor %}
                ],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',   // Création
                    'rgba(253, 126, 20, 0.7)',   // Production
                    'rgba(220, 53, 69, 0.7)',    // Achats
                    'rgba(32, 201, 151, 0.7)',   // Qualité
                    'rgba(111, 66, 193, 0.7)',   // Logistique
                    'rgba(255, 193, 7, 0.7)',    // Costing
                    'rgba(108, 117, 125, 0.7)'   // Autres
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(32, 201, 151, 1)',
                    'rgba(111, 66, 193, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(108, 117, 125, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Configuration du graphique
        const pieOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} jours (${percentage}%)`;
                        }
                    }
                }
            }
        };

        // Création du graphique
        new Chart(
            document.getElementById('timeDistributionChart'),
            {
                type: 'pie',
                data: timeDistributionData,
                options: pieOptions
            }
        );

        // Création du diagramme de workflow avec vis.js
        const container = document.getElementById('workflowDiagram');

        // Extraire les nœuds uniques des transitions
        const nodes = new Set();
        const edges = [];

        {% for transition, count in transitionCounts %}
            {% set states = transition|split(' -> ') %}
            nodes.add('{{ states[0] }}');
            nodes.add('{{ states[1] }}');

            edges.push({
                from: '{{ states[0] }}',
                to: '{{ states[1] }}',
                value: {{ count }},
                title: '{{ count }} transition(s)',
                arrows: 'to'
            });
        {% endfor %}

        // Convertir les ensembles en tableaux pour vis.js
        const nodesArray = Array.from(nodes).map(node => ({
            id: node,
            label: node,
            title: node
        }));

        // Créer les données pour le réseau
        const data = {
            nodes: new vis.DataSet(nodesArray),
            edges: new vis.DataSet(edges)
        };

        // Options pour le réseau
        const options = {
            nodes: {
                shape: 'box',
                margin: 10,
                font: {
                    size: 14
                },
                color: {
                    background: '#e7f1ff',
                    border: '#0d6efd',
                    highlight: {
                        background: '#0d6efd',
                        border: '#0a58ca'
                    }
                }
            },
            edges: {
                width: function(edge) {
                    return Math.sqrt(edge.value);
                },
                color: {
                    color: '#6c757d',
                    highlight: '#0d6efd'
                },
                smooth: {
                    type: 'curvedCW',
                    roundness: 0.2
                }
            },
            physics: {
                stabilization: true,
                barnesHut: {
                    gravitationalConstant: -5000,
                    centralGravity: 0.3,
                    springLength: 150,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            layout: {
                hierarchical: {
                    direction: 'LR',
                    sortMethod: 'directed',
                    levelSeparation: 150,
                    nodeSpacing: 100
                }
            }
        };

        // Créer le réseau
        const network = new vis.Network(container, data, options);
    });
</script>
{% endblock %}
