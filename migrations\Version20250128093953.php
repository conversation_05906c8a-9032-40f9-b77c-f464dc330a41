<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250128093953 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE commentaire (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, documents_id INT DEFAULT NULL, state VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', commentaire LONGTEXT NOT NULL, type VARCHAR(255) NOT NULL, INDEX IDX_67F068BCA76ED395 (user_id), INDEX IDX_67F068BC5F0F2752 (documents_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document (id INT AUTO_INCREMENT NOT NULL, rel_pack_id INT DEFAULT NULL, reference VARCHAR(255) NOT NULL, ref_rev VARCHAR(255) NOT NULL, ref_title_fra VARCHAR(255) DEFAULT NULL, prod_draw VARCHAR(255) DEFAULT NULL, prod_draw_rev VARCHAR(255) DEFAULT NULL, alias VARCHAR(255) DEFAULT NULL, doc_type VARCHAR(255) DEFAULT NULL, material_type VARCHAR(255) DEFAULT NULL, proc_type VARCHAR(255) DEFAULT NULL, inventory_impact VARCHAR(255) DEFAULT NULL, current_steps JSON NOT NULL, id_aletiq INT DEFAULT NULL, cust_drawing VARCHAR(255) DEFAULT NULL, cust_drawing_rev VARCHAR(255) DEFAULT NULL, action VARCHAR(255) DEFAULT NULL, ex VARCHAR(255) DEFAULT NULL, weight INT DEFAULT NULL, weight_unit VARCHAR(255) DEFAULT NULL, plating_surface INT DEFAULT NULL, plating_surface_unit VARCHAR(255) DEFAULT NULL, internal_mach_rec TINYINT(1) DEFAULT NULL, cls INT DEFAULT NULL, moq INT DEFAULT NULL, product_code VARCHAR(255) DEFAULT NULL, prod_agent VARCHAR(255) DEFAULT NULL, mof VARCHAR(255) DEFAULT NULL, commodity_code VARCHAR(255) DEFAULT NULL, purchasing_group VARCHAR(255) DEFAULT NULL, mat_prod_type VARCHAR(255) DEFAULT NULL, unit VARCHAR(255) DEFAULT NULL, leadtime INT DEFAULT NULL, pris_dans1 VARCHAR(255) DEFAULT NULL, pris_dans2 VARCHAR(255) DEFAULT NULL, eccn VARCHAR(255) DEFAULT NULL, rdo VARCHAR(255) DEFAULT NULL, hts VARCHAR(255) DEFAULT NULL, fia VARCHAR(255) DEFAULT NULL, metro_time INT DEFAULT NULL, q_inspection VARCHAR(255) DEFAULT NULL, q_dynamization VARCHAR(255) DEFAULT NULL, q_doc_rec VARCHAR(255) DEFAULT NULL, q_control_routing VARCHAR(255) DEFAULT NULL, critical_complete INT DEFAULT NULL, switch_aletiq TINYINT(1) DEFAULT NULL, material VARCHAR(255) DEFAULT NULL, metro_control LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', INDEX IDX_D8698A7648236B59 (rel_pack_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_status_history (id INT AUTO_INCREMENT NOT NULL, performed_by_id INT NOT NULL, document_id INT NOT NULL, previous_status VARCHAR(255) NOT NULL, new_status VARCHAR(255) NOT NULL, action VARCHAR(255) NOT NULL, action_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', comments LONGTEXT DEFAULT NULL, INDEX IDX_5527F2C22E65C292 (performed_by_id), INDEX IDX_5527F2C2C33F7837 (document_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE released_package (id INT AUTO_INCREMENT NOT NULL, owner_id INT DEFAULT NULL, verif_id INT DEFAULT NULL, valid_id INT DEFAULT NULL, project VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, activity VARCHAR(255) DEFAULT NULL, ex VARCHAR(255) DEFAULT NULL, reservation_date DATETIME DEFAULT NULL, creation_date DATETIME DEFAULT NULL, dmo JSON DEFAULT NULL, INDEX IDX_5D4403867E3C61F9 (owner_id), INDEX IDX_5D44038683162937 (verif_id), INDEX IDX_5D440386E48CA644 (valid_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user (id INT AUTO_INCREMENT NOT NULL, email VARCHAR(180) NOT NULL, username VARCHAR(255) DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, roles JSON NOT NULL, password VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_IDENTIFIER_EMAIL (email), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE visa (id INT AUTO_INCREMENT NOT NULL, released_drawing_id INT NOT NULL, validator_id INT NOT NULL, status VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, date_visa DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_16B1AB08CB7CF263 (released_drawing_id), INDEX IDX_16B1AB08B0644AEC (validator_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE messenger_messages (id BIGINT AUTO_INCREMENT NOT NULL, body LONGTEXT NOT NULL, headers LONGTEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', available_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', delivered_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_75EA56E0FB7336F0 (queue_name), INDEX IDX_75EA56E0E3BD61CE (available_at), INDEX IDX_75EA56E016BA31DB (delivered_at), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BCA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BC5F0F2752 FOREIGN KEY (documents_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A7648236B59 FOREIGN KEY (rel_pack_id) REFERENCES released_package (id)');
        $this->addSql('ALTER TABLE document_status_history ADD CONSTRAINT FK_5527F2C22E65C292 FOREIGN KEY (performed_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE document_status_history ADD CONSTRAINT FK_5527F2C2C33F7837 FOREIGN KEY (document_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D4403867E3C61F9 FOREIGN KEY (owner_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D44038683162937 FOREIGN KEY (verif_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D440386E48CA644 FOREIGN KEY (valid_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE visa ADD CONSTRAINT FK_16B1AB08CB7CF263 FOREIGN KEY (released_drawing_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE visa ADD CONSTRAINT FK_16B1AB08B0644AEC FOREIGN KEY (validator_id) REFERENCES user (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BCA76ED395');
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BC5F0F2752');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A7648236B59');
        $this->addSql('ALTER TABLE document_status_history DROP FOREIGN KEY FK_5527F2C22E65C292');
        $this->addSql('ALTER TABLE document_status_history DROP FOREIGN KEY FK_5527F2C2C33F7837');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D4403867E3C61F9');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D44038683162937');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D440386E48CA644');
        $this->addSql('ALTER TABLE visa DROP FOREIGN KEY FK_16B1AB08CB7CF263');
        $this->addSql('ALTER TABLE visa DROP FOREIGN KEY FK_16B1AB08B0644AEC');
        $this->addSql('DROP TABLE commentaire');
        $this->addSql('DROP TABLE document');
        $this->addSql('DROP TABLE document_status_history');
        $this->addSql('DROP TABLE released_package');
        $this->addSql('DROP TABLE user');
        $this->addSql('DROP TABLE visa');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
