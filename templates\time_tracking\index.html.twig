{% extends 'base.html.twig' %}

{% block title %}Suivi des temps{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        border: none;
    }

    .card-header {
        border-radius: 8px 8px 0 0 !important;
        font-weight: 600;
    }

    .search-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .badge {
        font-size: 0.85rem;
        padding: 0.4em 0.6em;
    }

    .table th {
        background-color: #f1f5f9;
        font-weight: 600;
    }

    .btn-info {
        background-color: #0dcaf0;
        border-color: #0dcaf0;
    }

    .btn-info:hover {
        background-color: #0bacdb;
        border-color: #0bacdb;
    }

    .state-column {
        min-width: 100px;
        text-align: center;
    }

    /* Styles pour la pagination manuelle */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .pagination .page-item {
        margin: 0 5px;
    }

    .pagination .page-link {
        border-radius: 4px;
    }

    .highlight {
        background-color: #fff3cd;
    }
</style>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <h1 class="mb-4">Suivi des temps de traitement des documents</h1>

    <!-- Formulaire de recherche avancée -->
    <div class="search-container mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="searchField" class="form-label">Champ de recherche</label>
                    <select id="searchField" class="form-select">
                        <option value="reference">Référence</option>
                        <option value="refRev">Révision</option>
                        <option value="refTitleFra">Titre</option>
                        <option value="currentSteps">Étape actuelle</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="searchValue" class="form-label">Valeur de recherche</label>
                    <input type="text" id="searchValue" class="form-control" placeholder="Saisir votre recherche...">
                </div>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button id="resetSearch" class="btn btn-secondary w-100">
                    <i class="fas fa-undo"></i> Réinitialiser
                </button>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Vue d'ensemble</h5>
                <span id="documentCount" class="badge bg-light text-dark">{{ timeData|length }} document(s)</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="timeTrackingTable">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Révision</th>
                            <th>Titre</th>
                            <th>Étape actuelle</th>
                            <th>Jours depuis BE</th>
                            {% for state in stateNames %}
                                <th class="state-column">{{ state }}</th>
                            {% endfor %}
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in timeData %}
                            <tr data-reference="{{ document.reference }}" data-refrev="{{ document.refRev }}" data-title="{{ document.refTitleFra }}" data-steps="{{ document.currentSteps|join(' ') }}">
                                <td>{{ document.reference }}</td>
                                <td>{{ document.refRev }}</td>
                                <td>{{ document.refTitleFra }}</td>
                                <td>
                                    {% for step in document.currentSteps %}
                                        <span class="badge bg-primary">{{ step }}</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    {% if document.daysSinceBE is not null %}
                                        <span class="badge bg-info">{{ document.daysSinceBE }} jour(s)</span>
                                    {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                    {% endif %}
                                </td>
                                {% for state in stateNames %}
                                    <td class="state-column">
                                        {% if document.states[state] is defined %}
                                            {% set stateData = document.states[state] %}
                                            <span class="badge {% if stateData.isActive %}bg-success{% else %}bg-secondary{% endif %}"
                                                  title="Passages: {{ stateData.entries }}">
                                                {{ stateData.totalDays ?? 0 }} jour(s)
                                            </span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">0</span>
                                        {% endif %}
                                    </td>
                                {% endfor %}
                                <td>
                                    <a href="{{ path('app_time_tracking_document', {'id': document.id}) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détails
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination manuelle -->
            <div class="pagination-container mt-4">
                <nav aria-label="Pagination des résultats">
                    <ul class="pagination" id="pagination">
                        <!-- Les boutons de pagination seront générés par JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Variables globales
        var allRows = $('#timeTrackingTable tbody tr');
        var itemsPerPage = 25;
        var currentPage = 1;

        // Fonction pour afficher les résultats paginés
        function showPage(page) {
            // Masquer toutes les lignes
            allRows.hide();

            // Filtrer les lignes visibles selon la recherche
            var filteredRows = filterRows();

            // Calculer les indices de début et de fin pour la pagination
            var startIndex = (page - 1) * itemsPerPage;
            var endIndex = startIndex + itemsPerPage;

            // Afficher les lignes de la page courante
            filteredRows.slice(startIndex, endIndex).show();

            // Mettre à jour le compteur de documents
            $('#documentCount').text(filteredRows.length + ' document(s)');

            // Mettre à jour la pagination
            updatePagination(filteredRows.length);
        }

        // Fonction pour filtrer les lignes selon la recherche
        function filterRows() {
            var searchField = $('#searchField').val();
            var searchValue = $('#searchValue').val().toLowerCase();

            if (!searchValue) {
                return allRows; // Retourner toutes les lignes si pas de recherche
            }

            // Filtrer les lignes selon le critère de recherche
            return allRows.filter(function() {
                var row = $(this);
                var fieldValue = '';

                switch(searchField) {
                    case 'reference':
                        fieldValue = row.data('reference') || '';
                        break;
                    case 'refRev':
                        fieldValue = row.data('refrev') || '';
                        break;
                    case 'refTitleFra':
                        fieldValue = row.data('title') || '';
                        break;
                    case 'currentSteps':
                        fieldValue = row.data('steps') || '';
                        break;
                }

                return fieldValue.toLowerCase().includes(searchValue);
            });
        }

        // Fonction pour mettre à jour la pagination
        function updatePagination(totalItems) {
            var totalPages = Math.ceil(totalItems / itemsPerPage);
            var paginationHtml = '';

            // Ne pas afficher la pagination s'il n'y a qu'une seule page
            if (totalPages <= 1) {
                $('#pagination').empty();
                return;
            }

            // Bouton précédent
            paginationHtml += '<li class="page-item ' + (currentPage === 1 ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="prev">&laquo;</a></li>';

            // Pages
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, startPage + 4);

            for (var i = startPage; i <= endPage; i++) {
                paginationHtml += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">';
                paginationHtml += '<a class="page-link" href="#" data-page="' + i + '">' + i + '</a></li>';
            }

            // Bouton suivant
            paginationHtml += '<li class="page-item ' + (currentPage === totalPages ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="next">&raquo;</a></li>';

            // Mettre à jour le HTML de la pagination
            $('#pagination').html(paginationHtml);

            // Ajouter les événements de clic
            $('.page-link').on('click', function(e) {
                e.preventDefault();

                var page = $(this).data('page');

                if (page === 'prev') {
                    if (currentPage > 1) {
                        currentPage--;
                    }
                } else if (page === 'next') {
                    if (currentPage < totalPages) {
                        currentPage++;
                    }
                } else {
                    currentPage = parseInt(page);
                }

                showPage(currentPage);
            });
        }

        // Événements de recherche
        $('#searchValue').on('keyup', function() {
            currentPage = 1; // Réinitialiser à la première page lors d'une recherche
            showPage(currentPage);
        });

        $('#searchField').on('change', function() {
            currentPage = 1; // Réinitialiser à la première page lors d'un changement de champ
            showPage(currentPage);
        });

        // Réinitialiser la recherche
        $('#resetSearch').on('click', function() {
            $('#searchValue').val('');
            $('#searchField').val('reference');
            currentPage = 1;
            showPage(currentPage);
        });

        // Initialisation : afficher la première page
        showPage(1);
    });
</script>
{% endblock %}
