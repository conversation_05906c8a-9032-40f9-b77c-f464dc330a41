<?php

namespace App\Command;

use App\Service\PerformanceMonitoringService;
use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:optimize-performance',
    description: 'Optimise les performances de la base de données en ajoutant des index et en analysant les requêtes'
)]
class OptimizePerformanceCommand extends Command
{
    private Connection $connection;
    private PerformanceMonitoringService $performanceService;

    public function __construct(Connection $connection, PerformanceMonitoringService $performanceService)
    {
        $this->connection = $connection;
        $this->performanceService = $performanceService;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('add-indexes', null, InputOption::VALUE_NONE, 'Ajouter les index de performance')
            ->addOption('analyze', null, InputOption::VALUE_NONE, 'Analyser les performances actuelles')
            ->addOption('recommendations', null, InputOption::VALUE_NONE, 'Afficher les recommandations d\'optimisation')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Simuler les changements sans les appliquer');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');

        $io->title('Optimisation des performances de la base de données');

        if ($input->getOption('analyze')) {
            $this->analyzePerformance($io);
        }

        if ($input->getOption('recommendations')) {
            $this->showRecommendations($io);
        }

        if ($input->getOption('add-indexes')) {
            $this->addPerformanceIndexes($io, $dryRun);
        }

        if (!$input->getOption('analyze') && !$input->getOption('recommendations') && !$input->getOption('add-indexes')) {
            // Par défaut, faire une analyse complète
            $this->analyzePerformance($io);
            $this->showRecommendations($io);
            
            if ($io->confirm('Voulez-vous ajouter les index de performance ?', false)) {
                $this->addPerformanceIndexes($io, false);
            }
        }

        $io->success('Optimisation des performances terminée.');
        return Command::SUCCESS;
    }

    private function analyzePerformance(SymfonyStyle $io): void
    {
        $io->section('Analyse des performances actuelles');

        $stats = $this->performanceService->getPerformanceStats();

        $io->table(
            ['Métrique', 'Valeur'],
            [
                ['Total documents', number_format($stats['total_documents'])],
                ['Documents avec étapes', number_format($stats['documents_with_steps'])],
                ['Documents avec timestamps', number_format($stats['documents_with_timestamps'])],
                ['Total visas', number_format($stats['total_visas'])],
                ['Taille moyenne current_steps', round($stats['avg_current_steps_size'] ?? 0, 2)],
                ['Taille moyenne timestamps', round($stats['avg_timestamps_size'] ?? 0, 2)],
            ]
        );

        // Analyser les index existants
        $io->section('Index existants sur la table document');
        $indexes = $this->connection->executeQuery("SHOW INDEX FROM document")->fetchAllAssociative();
        
        $indexTable = [];
        foreach ($indexes as $index) {
            $indexTable[] = [
                $index['Key_name'],
                $index['Column_name'],
                $index['Index_type'],
                $index['Cardinality'] ?? 'N/A'
            ];
        }
        
        $io->table(['Nom', 'Colonne', 'Type', 'Cardinalité'], $indexTable);
    }

    private function showRecommendations(SymfonyStyle $io): void
    {
        $io->section('Recommandations d\'optimisation');

        $recommendations = $this->performanceService->getOptimizationRecommendations();

        if (empty($recommendations)) {
            $io->success('Aucune recommandation d\'optimisation nécessaire pour le moment.');
            return;
        }

        foreach ($recommendations as $rec) {
            $color = match($rec['priority']) {
                'HIGH' => 'red',
                'MEDIUM' => 'yellow',
                'LOW' => 'green',
                default => 'white'
            };

            $io->block([
                "Priorité: {$rec['priority']}",
                "Catégorie: {$rec['category']}",
                "Titre: {$rec['title']}",
                "Description: {$rec['description']}",
                "Impact: {$rec['impact']}"
            ], null, "fg={$color}");
        }
    }

    private function addPerformanceIndexes(SymfonyStyle $io, bool $dryRun): void
    {
        $io->section('Ajout des index de performance');

        $indexQueries = [
            // Index pour current_steps JSON
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be_0 ON document ((JSON_EXTRACT(current_steps, '$.BE_0')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be_1 ON document ((JSON_EXTRACT(current_steps, '$.BE_1')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_be ON document ((JSON_EXTRACT(current_steps, '$.BE')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_produit ON document ((JSON_EXTRACT(current_steps, '$.Produit')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_qual_logistique ON document ((JSON_EXTRACT(current_steps, '$.Qual_Logistique')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_logistique ON document ((JSON_EXTRACT(current_steps, '$.Logistique')))",
            "CREATE INDEX IF NOT EXISTS idx_current_steps_costing ON document ((JSON_EXTRACT(current_steps, '$.Costing')))",
            
            // Index pour state_timestamps
            "CREATE INDEX IF NOT EXISTS idx_state_timestamps_not_null ON document (state_timestamps) WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''",
            
            // Index composites
            "CREATE INDEX IF NOT EXISTS idx_document_supervisor_timestamps ON document (superviseur_id, state_timestamps) WHERE state_timestamps IS NOT NULL",
            "CREATE INDEX IF NOT EXISTS idx_visa_released_drawing_name_status ON visa (released_drawing_id, name, status)",
            "CREATE INDEX IF NOT EXISTS idx_visa_validator_date ON visa (validator_id, date_visa)",
            
            // Index pour les champs fréquemment utilisés
            "CREATE INDEX IF NOT EXISTS idx_document_reference_rev ON document (reference, ref_rev)",
            "CREATE INDEX IF NOT EXISTS idx_document_material ON document (material)",
            "CREATE INDEX IF NOT EXISTS idx_document_proc_type ON document (proc_type)",
            "CREATE INDEX IF NOT EXISTS idx_document_doc_type ON document (doc_type)",
            "CREATE INDEX IF NOT EXISTS idx_document_pris_dans1 ON document (pris_dans1)",
        ];

        $progressBar = $io->createProgressBar(count($indexQueries));
        $progressBar->start();

        $successCount = 0;
        $errorCount = 0;

        foreach ($indexQueries as $query) {
            try {
                if (!$dryRun) {
                    $this->connection->executeStatement($query);
                }
                $successCount++;
                
                if ($dryRun) {
                    $io->writeln("\n[DRY RUN] Exécuterait: " . substr($query, 0, 80) . "...");
                }
            } catch (\Exception $e) {
                $errorCount++;
                $io->writeln("\n<error>Erreur lors de l'exécution: " . $e->getMessage() . "</error>");
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $io->newLine(2);

        if ($dryRun) {
            $io->info("Mode simulation: {$successCount} index seraient créés, {$errorCount} erreurs détectées.");
        } else {
            $io->success("{$successCount} index créés avec succès, {$errorCount} erreurs.");
        }

        // Analyser l'impact des nouveaux index
        if (!$dryRun && $successCount > 0) {
            $io->section('Analyse de l\'impact des nouveaux index');
            
            // Vérifier la taille des index
            $indexSizes = $this->connection->executeQuery("
                SELECT 
                    INDEX_NAME,
                    ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'Index Size (MB)'
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'document'
                AND INDEX_NAME LIKE 'idx_%'
                GROUP BY INDEX_NAME
                ORDER BY INDEX_LENGTH DESC
            ")->fetchAllAssociative();
            
            if (!empty($indexSizes)) {
                $io->table(['Nom de l\'index', 'Taille (MB)'], $indexSizes);
            }
        }
    }
}
