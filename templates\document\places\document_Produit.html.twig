{% extends 'base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}

<style>
#table-container{
    padding: 1rem;
    background-color: #F8F9FA;
    border-radius: 0.5rem;
}

#table tr:last-child {
    border: none!important;
}


.table-filter-input {
    width: 100%;
    font-size: 0.85rem;
    height: calc(1.8rem + 2px);
}

#table td span {
    cursor: pointer;
    transition: all 0.3s;
}
#table td span:hover {
    padding: 0.375rem 0.75rem;
}


#table th, #table td {
    vertical-align: middle!important;
    white-space: nowrap;
    text-align: center!important;
    padding: 0.15rem!important;
    border: none!important;
}

#table thead th {
    user-select: none;
}

#table thead tr{
    border: none;
}


#table thead tr#entetes th {
    background-color: #004080;
    color: #fff;
    font-size: 0.85rem;
}

#table thead tr#filtres th {
    background-color: #F8F9FA; /* gris clair */
    border: none;
    cursor: pointer;
}

/* Icônes de tri */
th.sort-asc i, th.sort-desc i {
    margin-left: 5px;
}

/* Badges */
.badge.bg-primary {
    background:  #0059B3!important;
}

/* Champs invalides */
.is-invalid {
    border-color: #dc3545;
}

/* Boutons */
.btn-refresh {
    margin-bottom: 1rem;
}

/* --- Modale personnalisée --- */
.modal-dialog.modal-lg {
    max-width: 900px;
}

.modal-content.custom-modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.modal-header.custom-modal-header {
    background-color: #004080;
    color: #fff;
    border-bottom: none;
}

.modal-header.custom-modal-header .btn-close {
    filter: invert(100%); /* Rendre la croix blanche sur fond bleu */
}

.modal-footer.custom-modal-footer {
    border-top: none;
}

.tooltip .tooltip-inner {
max-width: 400px;
overflow-y: auto;   /* Barre de défilement si dépasse */
white-space: normal; /* Permet de passer à la ligne */
}


/* Styles pour la pagination */
.pagination-container {
    margin: 20px 0;
}

.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: #004080;
    border-color: #004080;
}

.pagination-container .page-link {
    color: #004080;
}

.pagination-container .page-link:hover {
    color: #002040;
    background-color: #e9ecef;
}
</style>
<datalist id="productCode">
</datalist>
<datalist id="eccn">
</datalist>


<div class="mt-3" style="margin: 0 2%">
    <div class="row">
        <div class="col">
            <h3 class="mb-2">Révisions des Produits</h3>

           {% if documents is empty %}
                <div class="alert alert-warning" role="alert">
                    Aucun document trouvé.
                </div>
            {% else %}
                <div class="card shadow border-0">
                    <div class="card-body p-0">
                        <div id="table-container" >
                            <table class="table table-hover table-bordered mb-0" id="table">
                                <thead>
                                    <!-- Ligne de filtres -->
                                    <tr id="filtres">
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="#" data-col="0"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Pack" data-col="1"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Activité" data-col="2"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Référence" data-col="3"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="4"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="prod plan" data-col="5"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="6"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Titre" data-col="7"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Action" data-col="8"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Inventaire" data-col="9"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Type" data-col="10"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="RDO" data-col="11"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="CLS" data-col="12"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="MOQ" data-col="13"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Code Produit" data-col="14"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="ECCN" data-col="15"></th>
                                        <th colspan="2"><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Commentaires" data-col="16"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Étapes" data-col="18"></th>
                                        <th><span id="signer-mass" style="display: none" class="badge bg-success" onclick="signSelectedDocuments()"> Signer sélectionnés </span> </th>
                                        <th><span class="badge bg-secondary">{{documents|length}}{{ documents|length == 1 ? ' Document' : ' Documents' }}</span></th>
                                    </tr>
                                    <!-- Ligne d'entêtes -->
                                    <tr id="entetes">
                                        <th>jours</th>
                                        <th>Pack</th>
                                        <th>Activité</th>
                                        <th>Référence</th>
                                        <th>R</th>
                                        <th>prod plan</th>
                                        <th>R</th>
                                        <th>Titre</th>
                                        <th>Action</th>
                                        <th>Inventaire</th>
                                        <th>Type</th>
                                        <th>RDO</th>
                                        <th>CLS</th>
                                        <th>MOQ</th>
                                        <th>Code Produit</th>
                                        <th>ECCN</th>
                                        <th colspan="2">Commentaires</th>
                                        <th>Révisions</th>
                                        <th>Validations</th>
                                        <th>Visas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for document in documents %}
                                    <tr document-id="{{ document.id }}">
                                        <td>
                                          {% if document.stateTimestamps is not null %}
                                            {% if document.stateTimestamps[place] is defined %}
                                                {% set arrivedDate = date(document.stateTimestamps[place]) %}
                                                {% set diff = date().diff(arrivedDate) %}
                                                <span class="badge bg-primary">{{ diff.days }} jour{{ diff.days > 1 ? 's' }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}

                                        {% else %}
                                            <span class="badge bg-secondary">N/A</span>
                                        {% endif %}
                                            {# {{document.getDaysInState(place)}} #}
                                        </td>
                                        <td><a href="{{ path('detail_package', {'id': document.relPack.id}) }}" class="badge bg-primary pack-link">{{document.relPack.id}}</a></td>
                                        <td>
                                            <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                <tbody>
                                                    <tr>
                                                        <td class="p-1" style="font-size: 0.70rem;"><strong>activité</strong> {{ document.relPack.activity }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="p-1" style="font-size: 0.70rem;"><strong>Projet</strong> {{ document.relPack.getProjectRelation() ? document.relPack.getProjectRelation.otp() : '' }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td>{{ document.reference|trim }}</td>
                                        <td>
                                            {{ document.refrev }}
                                            {% if document.ex != 'NO' %}
                                                <span style="color: red; font-weight: 500;"><sup>{{ document.ex }}</sup></span>
                                            {% else %}
                                                <sup>{{ document.ex }}</sup>
                                            {% endif %}
                                        </td>
                                        <td class="p-1" style="font-size: 0.80rem;">
                                            <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                                target="_blank"
                                                class="badge bg-primary preview-tooltip">
                                                {{ document.prodDraw }}
                                            </a>
                                        </td>

                                        {# <td class="p-1" style="font-size: 0.80rem;"><a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}" target="_blank" class="badge bg-primary">{{ document.prodDraw }}</a></td> #}
                                        <td>{{ document.prodDrawRev }}</td>
                                        <td>{{ document.reftitlefra|trim }}</td>
                                        <td>{{ document.action }}</td>
                                        <td>{{ document.getInventoryImpact }}</td>
                                        <td class="p-1">
                                            <select class="form-control form-control-sm doc-type-select"
                                                    name="doctype"
                                                    doc-id="{{ document.id }}">
                                                <option value="ASSY" {% if document.doctype == 'ASSY' %}selected{% endif %}>ASSY</option>
                                                <option value="MACH" {% if document.doctype == 'MACH' %}selected{% endif %}>MACH</option>
                                                <option value="MOLD" {% if document.doctype == 'MOLD' %}selected{% endif %}>MOLD</option>
                                                <option value="DOC" {% if document.doctype == 'DOC' %}selected{% endif %}>DOC</option>
                                                <option value="PUR" {% if document.doctype == 'PUR' %}selected{% endif %}>PUR</option>
                                            </select>
                                        </td>
                                        <td>{{ document.rdo }}</td>

                                        <td>
                                            <input type="text"
                                                   class="form-control form-control-sm cls-input"
                                                   value="{{ document.cls }}"
                                                   doc-id="{{ document.id }}">
                                        </td>
                                        <td>
                                            <input type="text"
                                                   class="form-control form-control-sm moq-input"
                                                   value="{{ document.moq }}"
                                                   doc-id="{{ document.id }}">
                                        </td>
                                        <td>
                                            <input class="form-control form-control-sm productCode-input"
                                                list="productCode" name="ice-cream-choice"
                                                value="{{ document.productCode }}"
                                                doc-id="{{ document.id }}">
                                        </td>
                                        <td>
                                            <input class="form-control form-control-sm eccn-input"
                                                list="eccn" name="ice-cream-choice"
                                                value="{{ document.eccn }}"
                                                doc-id="{{ document.id }}">
                                        </td>
                                       <td>
                                            <i class="fas fa-file-alt"
                                            data-bs-toggle="tooltip"
                                            data-document-id="{{ document.id }}"
                                            onmouseenter="loadCommentsTooltip(this)"
                                            txt="{% for comment in document.commentaires %}
                                            <strong>{{ comment.state|e }}</strong> : {{ comment.commentaire|e }}
                                            par <em>{{ comment.user|e }}</em><br>
                                            {% endfor %}"
                                            ></i>
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                class="form-control form-control-sm comment-input"
                                                placeholder="Commentaire"
                                                data-document-id="{{ document.id }}"
                                            />
                                        </td>
                                        <td class="text-center">
                                            {% if document.CurrentStepsVisa|length > 1 %}
                                                <div class="dropdown">
                                                    <span id="dropdownMenuButton{{ document.id }}"
                                                          data-bs-toggle="dropdown"
                                                          aria-expanded="false"
                                                          class="badge bg-primary">
                                                          {{ document.CurrentStepsVisa|length }}
                                                    </span>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ document.id }}">
                                                        {% for step, value in document.CurrentStepsVisa %}
                                                            <li><a class="dropdown-item" href="{{ path('app_document_place', {'place': step}) }}">{{ step|replace({'_': ' '})|first|upper ~ step|replace({'_': ' '})|slice(1) }}</a></li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            {% else %}
                                                {% for step, value in document.CurrentStepsVisa %}
                                                    <span class="badge bg-primary">{{ step }}</span>
                                                {% endfor %}
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex justify-content-center align-items-center">
                                                <div class="form-check">
                                                   <input class="form-check-input doc-select"
                                                    type="checkbox"
                                                    data-document-id="{{ document.id }}"
                                                    data-current-steps='{{ document.CurrentStepsVisa|json_encode|e('html_attr') }}'>

                                                </div>
                                                <span class="badge bg-primary" onclick='createVisa("{{ document.id|escape('js') }}")'>Signe</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary" onclick="showVisas({{ document.id }})"><i class="fa-solid fa-passport"></i></span>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                            <!-- Contrôles de pagination -->
                        </div><!-- ./table-responsive -->
                    </div><!-- ./card-body -->
                </div><!-- ./card -->
                {# Pagination supprimée #}
            {% endif %}
        </div><!-- ./col -->
    </div><!-- ./row -->
</div><!-- ./container -->

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>


{% include 'js/jhess.html.twig' %}
{% endblock %}

