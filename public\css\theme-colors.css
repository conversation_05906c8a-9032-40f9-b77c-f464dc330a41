/* 
 * Palette de couleurs cohérente pour SCM Portal
 * Couleur principale: #009BFF
 */

:root {
    /* Couleurs principales */
    --primary-color: #009BFF;
    --primary-dark: #0056b3;
    --primary-light: #33aaff;
    --primary-lighter: #66bbff;
    
    /* Couleurs secondaires */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Couleurs de fond */
    --bg-primary: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    
    /* Couleurs de texte */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-light: #ffffff;
    --text-muted: #6c757d;
    
    /* Ombres */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 155, 255, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 155, 255, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 155, 255, 0.175);
    
    /* Bordures */
    --border-color: #dee2e6;
    --border-primary: #009BFF;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
}

/* Classes utilitaires pour les couleurs */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background: var(--bg-primary) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

.btn-primary-custom {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    color: var(--text-light);
    transition: all 0.2s ease;
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004080 100%);
    border-color: var(--primary-dark);
    color: var(--text-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline-primary-custom {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    transition: all 0.2s ease;
}

.btn-outline-primary-custom:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-light);
    transform: translateY(-1px);
}

/* Liens avec couleur cohérente */
.link-primary-custom {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.link-primary-custom:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Cards avec thème cohérent */
.card-primary-custom {
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card-primary-custom:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

/* Badges avec couleur cohérente */
.badge-primary-custom {
    background: var(--bg-primary);
    color: var(--text-light);
    font-weight: 600;
}

/* Alertes avec couleur cohérente */
.alert-primary-custom {
    background: rgba(0, 155, 255, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

/* Tables avec thème cohérent */
.table-primary-custom thead th {
    background: var(--bg-primary);
    color: var(--text-light);
    border: none;
}

.table-primary-custom tbody tr:hover {
    background: rgba(0, 155, 255, 0.05);
}

/* Formulaires avec thème cohérent */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 155, 255, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 155, 255, 0.25);
}

/* Pagination avec thème cohérent */
.pagination .page-link {
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: var(--bg-primary);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    color: var(--primary-dark);
    background: rgba(0, 155, 255, 0.1);
    border-color: var(--primary-color);
}

/* Dropdown avec thème cohérent */
.dropdown-item:hover,
.dropdown-item:focus {
    background: rgba(0, 155, 255, 0.1);
    color: var(--primary-color);
}

/* Progress bars avec thème cohérent */
.progress-bar-primary-custom {
    background: var(--bg-primary);
}

/* Spinners avec thème cohérent */
.spinner-border-primary-custom {
    color: var(--primary-color);
}

/* Tooltips avec thème cohérent */
.tooltip-inner {
    background: var(--primary-color);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--primary-color);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--primary-color);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--primary-color);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--primary-color);
}
