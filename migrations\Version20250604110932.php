<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604110932 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute la colonne etat à product_range et legacy_id à dmo pour la migration';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_range ADD etat TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE dmo ADD legacy_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_range DROP etat');
        $this->addSql('ALTER TABLE dmo DROP legacy_id');
    }
}
