<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604112739 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rend la colonne dmo nullable pour permettre la création de nouveaux DMO';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE dmo CHANGE dmo dmo VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE dmo CHANGE dmo dmo VARCHAR(255) NOT NULL');
    }
}
