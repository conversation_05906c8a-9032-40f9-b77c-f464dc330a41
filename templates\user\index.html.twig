{% extends 'baseImputation.html.twig' %}

{% block title %}Gestion des Utilisateurs{% endblock %}

{% block body %}
<style>
    .bg-bleu {
        background-color: #0059B3!important;
    }

    td {
        white-space: nowrap;
    }
</style>

<div style="margin: 0 1%" class="row">
    <div class="col-md-4">
        <h3>Utilisateurs</h3>
        <table class="table table-sm table-bordered table-hover">
            <thead class="table-light">
                <tr>
                    <th class="text-center align-middle bg-bleu text-white border-0">Nom</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Prénom</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Departement</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Ajouter</th>
                </tr>
            </thead>
            <tbody id="user-without-imputation">
            </tbody>
        </table>
    </div>
    <div class="col-md-8">
        <div class="d-flex justify-content-between align-items-center">
            <h3>Imputation</h3>
            <div>
                <a href="" class="badge bg-warning" >Mail</a>
            </div>
        </div>
        <table class="table table-sm table-bordered table-hover">
            <thead class="table-light">
                <tr>
                    <th class="text-center align-middle bg-bleu text-white border-0">Retirer</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Nom</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Prénom</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Email</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Departement</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">Centre de coût</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">CI</th>
                    <th class="text-center align-middle bg-bleu text-white border-0">SAP</th>
                </tr>
            </thead>
            <tbody id="user-with-imputation">
            </tbody>
        </table>
    </div>
</div>

{# modal select to choose a workCenter to an user #}
<div class="modal fade" id="modalWorkCenter" tabindex="-1" aria-labelledby="modalWorkCenterLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-bleu text-white">
                <h5 class="modal-title" id="modalWorkCenterLabel">Centre de coût</h5>
            </div>
            <div class="modal-body">
                <select class="form-select" id="selectWorkCenter">
                    <option value="">Choisir un centre de coût</option>
                    {% for workCenter, title in workCenters %}
                        <option value="{{ workCenter }}">{{ workCenter }} - {{ title }}</option>
                    {% endfor %}
                </select>
                <input type="hidden" id="userId">
            </div>
            <div class="modal-footer d-flex justify-content-between align-items-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="btnSaveWorkCenter">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<script>
const workCenter_title = {{ workCenters|json_encode|raw }};

function fillTableImputation() {
    $.ajax({
        url: "{{ path('get_user_imputation') }}",
        type: 'GET',
        success: function(response) {
            $('#user-with-imputation').empty();
            var mail = '';
            response.forEach(function(user) {
                mail += user.email + '; ';
                var tr = $('<tr>');
                tr.append('<td class="text-center align-middle"><span data-id="' + user.id + '" class="switch-impute badge bg-danger" style="cursor: pointer"><i class="fa-solid fa-arrow-left"></i></span></td>');
                tr.append('<td class="text-center align-middle">' + user.nom + '</td>');
                tr.append('<td class="text-center align-middle">' + user.prenom + '</td>');
                tr.append('<td class="text-center align-middle">' + user.email + '</td>');
                tr.append('<td class="text-center align-middle">' + user.departement + '</td>');
                tr.append('<td class="align-middle"> <span data-id="' + user.id + '" class="badge bg-primary editWorkcenter me-2" style="cursor: pointer" ><i class="fa-solid fa-edit"></i></span>' + (user.workCenter ? user.workCenter + ' - ' + workCenter_title[user.workCenter] : '') + '</td>');
                tr.append('<td class="text-center align-middle"><input type="checkbox" ' + (user.ci ? 'checked' : '') + ' class="check" data-typeimpute="ci" data-id="' + user.id + '"></td>');
                tr.append('<td class="text-center align-middle"><input type="checkbox" ' + (user.sap ? 'checked' : '') + ' class="check" data-typeimpute="sap" data-id="' + user.id + '"></td>');
                $('#user-with-imputation').append(tr);
            });
            $('.badge.bg-warning').attr('href', 'mailto:' + mail);
        }
    });
}

function fillTableWithoutImputation() {
    $.ajax({
        url: "{{ path('get_user_no_imputation') }}",
        type: 'GET',
        success: function(response) {
            $('#user-without-imputation').empty();
            response.forEach(function(user) {
                var tr = $('<tr>');
                tr.append('<td class="text-center align-middle">' + user.nom + '</td>');
                tr.append('<td class="text-center align-middle">' + user.prenom + '</td>');
                tr.append('<td class="text-center align-middle">' + user.departement + '</td>');
                tr.append('<td class="text-center align-middle"><span data-id="' + user.id + '" class="switch-impute badge bg-primary" style="cursor: pointer"><i class="fa-solid fa-arrow-right"></i></span></td>');
                $('#user-without-imputation').append(tr);
            });
        }
    });
}

function switchImputation(id) {
    url = "{{ path('switch_user_imputation', {'id': 'EDIT_ME'}) }}";
    url = url.replace('EDIT_ME', id);
    $.ajax({
        url: url,
        type: 'PUT',
        success: function(response) {
            refreshTable();
        }
    });
}

function switchCheck(id, type) {
    url = "{{ path('switch_user_check', {'id': 'EDIT_ME', 'type': 'EDIT_TYPE'}) }}";
    url = url.replace('EDIT_ME', id);
    url = url.replace('EDIT_TYPE', type);
    $.ajax({
        url: url,
        type: 'PUT',
        success: function(response) {
            Toast.fire({
                icon: 'success',
                title: 'Mise à jour effectuée'
            });
        }
    });
}

function refreshTable() {
    fillTableImputation();
    fillTableWithoutImputation();
}

$(document).on('click', '.switch-impute', function() {
    $(this).removeClass('switch-impute');
    switchImputation($(this).data('id'));
});

$(document).on('change', '.check', function() {
    switchCheck($(this).data('id'), $(this).data('typeimpute'));
});

$(document).ready(function() {
    refreshTable();
});

$(document).on('click', '.editWorkcenter', function() {
    $('#userId').val($(this).data('id'));
    $('#modalWorkCenter').modal('show');
});

$(document).on('click', '#btnSaveWorkCenter', function() {
    var userId = $('#userId').val();
    var workCenter = $('#selectWorkCenter').val();
    $.ajax({
        url: "{{ path('switch_user_workcenter', {'id': 'EDIT_ME'}) }}".replace('EDIT_ME', userId),
        type: 'PUT',
        data: {
            workCenter: workCenter
        },
        success: function(response) {
            $('#modalWorkCenter').modal('hide');
            refreshTable();
        }
    });
});
</script>

{% endblock %}

