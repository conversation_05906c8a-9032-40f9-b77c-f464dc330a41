<?php

namespace App\Command;

use App\Repository\UserRepository;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class PopulateLdapUsersCommand extends Command
{
    // Le nom de la commande qui sera exécutée en ligne de commande
    protected static $defaultName = 'app:populate-ldap-users';
    
    // Vous pouvez également définir une description
    protected static $defaultDescription = 'Peuple la base de données avec tous les utilisateurs issus du LDAP';

    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        parent::__construct(self::$defaultName);
        $this->userRepository = $userRepository;
    }
    
    protected function configure(): void
    {
        $this
            ->setDescription(self::$defaultDescription)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Utilisation de SymfonyStyle pour une meilleure lisibilité des messages dans la console
        $io = new SymfonyStyle($input, $output);
        $io->title('Importation des utilisateurs LDAP');

        try {
            // Appel de la méthode qui parcourt tous les utilisateurs LDAP et les insère/actualise dans la base
            $this->userRepository->initializeUsers();
            $io->success('Les utilisateurs LDAP ont été importés avec succès.');
        } catch (\Exception $exception) {
            $io->error('Une erreur est survenue : ' . $exception->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
