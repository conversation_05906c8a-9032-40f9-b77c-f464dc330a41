<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250217140150 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE dmo (id INT AUTO_INCREMENT NOT NULL, requestor_id INT DEFAULT NULL, eng_owner_id INT DEFAULT NULL, last_modificator_id INT DEFAULT NULL, date_init DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', description LONGTEXT DEFAULT NULL, project VARCHAR(255) DEFAULT NULL, decision VARCHAR(255) NOT NULL, status TINYINT(1) NOT NULL, ex TINYINT(1) DEFAULT NULL, indus_related TINYINT(1) DEFAULT NULL, date_end DATETIME DEFAULT NULL, pr_number INT DEFAULT NULL, last_update_date DATETIME NOT NULL, ex_assessment LONGTEXT DEFAULT NULL, spent_time INT NOT NULL, type VARCHAR(255) NOT NULL, document VARCHAR(255) DEFAULT NULL, INDEX IDX_BDC1D3CDA7F43455 (requestor_id), INDEX IDX_BDC1D3CD4C484F77 (eng_owner_id), INDEX IDX_BDC1D3CD21AE072F (last_modificator_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CDA7F43455 FOREIGN KEY (requestor_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CD4C484F77 FOREIGN KEY (eng_owner_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CD21AE072F FOREIGN KEY (last_modificator_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE commentaire ADD dmo_id_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BC95289E08 FOREIGN KEY (dmo_id_id) REFERENCES dmo (id)');
        $this->addSql('CREATE INDEX IDX_67F068BC95289E08 ON commentaire (dmo_id_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BC95289E08');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CDA7F43455');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CD4C484F77');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CD21AE072F');
        $this->addSql('DROP TABLE dmo');
        $this->addSql('DROP INDEX IDX_67F068BC95289E08 ON commentaire');
        $this->addSql('ALTER TABLE commentaire DROP dmo_id_id');
    }
}
