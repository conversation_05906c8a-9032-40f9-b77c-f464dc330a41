<?php

namespace App\Entity;

use App\Repository\CodeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CodeRepository::class)]
class Code
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(length: 255)]
    private ?string $code = null;

    #[ORM\Column(nullable: true)]
    private ?int $level = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $workCenter = null;

    #[ORM\ManyToOne(inversedBy: 'codes')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Phase $phase = null;

    /**
     * @var Collection<int, Impute>
     */
    #[ORM\OneToMany(targetEntity: Impute::class, mappedBy: 'code', orphanRemoval: true)]
    private Collection $imputes;

    public function __construct()
    {
        $this->imputes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(?int $level): static
    {
        $this->level = $level;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getWorkCenter(): ?string
    {
        return $this->workCenter;
    }

    public function setWorkCenter(?string $workCenter): static
    {
        $this->workCenter = $workCenter;

        return $this;
    }

    public function getPhase(): ?Phase
    {
        return $this->phase;
    }

    public function setPhase(?Phase $phase): static
    {
        $this->phase = $phase;

        return $this;
    }

    /**
     * @return Collection<int, Impute>
     */
    public function getImputes(): Collection
    {
        return $this->imputes;
    }

    public function addImpute(Impute $impute): static
    {
        if (!$this->imputes->contains($impute)) {
            $this->imputes->add($impute);
            $impute->setCode($this);
        }

        return $this;
    }

    public function removeImpute(Impute $impute): static
    {
        if ($this->imputes->removeElement($impute)) {
            // set the owning side to null (unless already changed)
            if ($impute->getCode() === $this) {
                $impute->setCode(null);
            }
        }

        return $this;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'code' => $this->code,
            'level' => $this->level,
            'status' => $this->status,
            'workCenter' => $this->workCenter,
            'phase' => $this->phase->getCode() . ' - ' . $this->phase->getTitle(),
            'projet' => $this->phase->getProjet()->getOtp() . ' - ' . $this->phase->getProjet()->getTitle(),
            'phaseOpen' => $this->phase->isOpen(),
            'phaseStatus' => $this->phase->isStatus(),
            'phaseStatusManuel' => $this->phase->isStatusManuel(),
        ];
    }
}
