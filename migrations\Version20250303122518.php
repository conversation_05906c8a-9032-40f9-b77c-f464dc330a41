<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250303122518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE project (id INT AUTO_INCREMENT NOT NULL, project_manager_id INT DEFAULT NULL, otp VARCHAR(255) NOT NULL, title VARCHAR(500) NOT NULL, INDEX IDX_2FB3D0EE60984F51 (project_manager_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EE60984F51 FOREIGN KEY (project_manager_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD project_relation_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D440386FA9966A0 FOREIGN KEY (project_relation_id) REFERENCES project (id)');
        $this->addSql('CREATE INDEX IDX_5D440386FA9966A0 ON released_package (project_relation_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D440386FA9966A0');
        $this->addSql('ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EE60984F51');
        $this->addSql('DROP TABLE project');
        $this->addSql('DROP INDEX IDX_5D440386FA9966A0 ON released_package');
        $this->addSql('ALTER TABLE released_package DROP project_relation_id');
    }
}
