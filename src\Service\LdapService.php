<?php

namespace App\Service;

use Symfony\Component\Ldap\Ldap;
use Symfony\Component\Ldap\Entry;

class LdapService
{
    private $ldap;
    private $allScmMembers = null;

    public function __construct(string $host, string $username, string $password)
    {
        $this->ldap = Ldap::create('ext_ldap', [
            'connection_string' => "ldap://$host",
        ]);
        $this->ldap->bind($username, $password);
    }

    public function search(string $baseDn, string $filter, array $attributes = []): array
    {
        $query = $this->ldap->query($baseDn, $filter);
        $results = $query->execute();
        $entries = [];
        foreach ($results as $entry) {
            $entries[] = $entry;
        }

        return $entries;
    }

    public function getUserInfo(string $userDn): ?array
    {
        $results = $this->search($userDn, '(objectClass=*)', ['sAMAccountName', 'manager', 'objectClass']);

        if (empty($results)) {
            return null;
        }

        $entry = $results[0];
        preg_match('/CN=([^,]+)/', $userDn, $matches);
        $commonName = $matches[1] ?? null;

        // Vérifier si c'est un conteneur ou un utilisateur
        $isContainer = in_array('group', $entry->getAttribute('objectClass') ?? [], true);

        return [
            'username' => $entry->getAttribute('sAMAccountName')[0] ?? null,
            'distinguishedName' => $commonName,
            'manager' => isset($entry->getAttribute('manager')[0]) ? $this->getManagerInfo($entry->getAttribute('manager')[0]) : [],
            'isContainer' => $isContainer,
        ];
    }

    public function getManagerInfo(string $managerDn): ?array
    {
        $results = $this->search($managerDn, '(objectClass=*)', ['sAMAccountName']);

        if (empty($results)) {
            return null;
        }

        $entry = $results[0];
        preg_match('/CN=([^,]+)/', $managerDn, $matches);
        $commonName = $matches[1] ?? null;

        return [
            'username' => $entry->getAttribute('sAMAccountName')[0] ?? null,
            'distinguishedName' => $commonName,
        ];
    }

    public function getAllScmMembers(string $allScmDn): array
    {
        if ($this->allScmMembers === null) {
            $results = $this->search($allScmDn, '(objectClass=*)', ['member']);
            if (!empty($results) && isset($results[0])) {
                $members = $results[0]->getAttribute('member') ?? [];
                $this->allScmMembers = array_map(fn($dn) => $this->getUserInfo($dn), $members);
            } else {
                $this->allScmMembers = [];
            }
        }

        return $this->allScmMembers;
    }

    public function getUsersFromSpecificOUs(array $ouPaths): array
    {
        $allUsers = [];
        foreach ($ouPaths as $ouPath) {
            $this->fetchUsersRecursively($ouPath, $allUsers);
        }

        return $allUsers;
    }

    private function fetchUsersRecursively(string $baseDn, array &$allUsers, array &$visitedOUs = [])
    {
        // Éviter les cycles en vérifiant si cet OU a déjà été visité
        if (in_array($baseDn, $visitedOUs, true)) {
            return;
        }

        // Marquer cet OU comme visité
        $visitedOUs[] = $baseDn;

        // Récupérer les utilisateurs sous $baseDn
        $users = $this->search($baseDn, '(objectClass=user)', [
            'sAMAccountName',
            'mail',
            'title',
            'department',
            'manager',
            'distinguishedName',
        ]);

        preg_match('/OU=([^,]+)/', $baseDn, $matches);
        $ouName = $matches[1] ?? $baseDn;

        foreach ($users as $user) {
            $allUsers[] = [
                'username' => $user->getAttribute('sAMAccountName')[0] ?? null,
                'email' => $user->getAttribute('mail')[0] ?? null,
                'title' => $user->getAttribute('title')[0] ?? null,
                'department' => $user->getAttribute('department')[0] ?? null,
                'manager' => $user->getAttribute('manager')[0] ?? null,
                'distinguishedName' => $user->getAttribute('distinguishedName')[0] ?? null,
                'ouName' => $ouName,
            ];
        }

        if (empty($users)) {
            return;
        }

        $subOUs = $this->search($baseDn, '(objectClass=organizationalUnit)', ['distinguishedName']);
        foreach ($subOUs as $subOU) {
            $subDn = $subOU->getAttribute('distinguishedName')[0];
            $this->fetchUsersRecursively($subDn, $allUsers, $visitedOUs);
        }
    }



    public function getUserInfoByUsernameInscription(string $username): ?array
    {
        // Rechercher l'utilisateur par sAMAccountName
        $results = $this->search(
            'OU=FRSCM_Utilisateurs,DC=scmlemans,DC=com',
            "(sAMAccountName={$username})"
        );
    
        if (empty($results)) {
            return null;
        }
        

        $entry = $results[0];
        $distinguishedName = $entry->getAttribute('distinguishedName')[0] ?? null;
        return [
            'username' => $entry->getAttribute('sAMAccountName')[0] ?? null,
            'distinguishedName' => $distinguishedName,
            'manager' => isset($entry->getAttribute('manager')[0]) ? $this->getManagerInfo($entry->getAttribute('manager')[0]) : [],
            'email' => $entry->getAttribute('mail')[0] ?? null,
            'title' => $entry->getAttribute('title')[0] ?? null,
            'department' => $entry->getAttribute('department')[0] ?? null,
            'isManager' => $this->isManager($entry->getAttribute('sAMAccountName')[0] ?? ''), // Appel à isManager
        ];
    }
    
    public function isManager(string $username): bool
    {
        // Rechercher les informations de l'utilisateur pour obtenir son DN complet
        $userInfo = $this->search(
            'OU=FRSCM_Utilisateurs,DC=scmlemans,DC=com',
            "(sAMAccountName={$username})",
            ['distinguishedName']
        );
    
        if (empty($userInfo)) {
            return false;
        }
    
        $userDn = $userInfo[0]->getAttribute('distinguishedName')[0] ?? null;
        if (!$userDn) {
            return false;
        }
    
        // Vérifier si quelqu'un a cet utilisateur comme manager
        $results = $this->search(
            'OU=FRSCM_Utilisateurs,DC=scmlemans,DC=com',
            "(manager={$userDn})",
            ['sAMAccountName']
        );
    
        return !empty($results);
    }
    
    public function getAllUsersInfo(): array
    {
        // Rechercher tous les utilisateurs dans l'OU spécifiée
        $results = $this->search(
            'OU=FRSCM_Utilisateurs,DC=scmlemans,DC=com',
            "(objectClass=user)" // Filtre pour récupérer tous les utilisateurs
        );
    
        // Si aucun résultat n'est trouvé, retourner un tableau vide
        if (empty($results)) {
            return [];
        }
    
        $users = [];
        $admin=[];
        foreach ($results as $entry) {
            $distinguishedName = $entry->getAttribute('distinguishedName')[0] ?? null;
            // an dusername didnt include admin

            if ($entry->getAttribute('mail')[0] ?? null != null && $entry->getAttribute('mail')[0] != '<EMAIL>') {
                $users[] = [
                    'username' => $entry->getAttribute('sAMAccountName')[0] ?? null,
                    'distinguishedName' => $distinguishedName,
                    'manager' => isset($entry->getAttribute('manager')[0]) ? $this->getManagerInfo($entry->getAttribute('manager')[0]) : [],
                    'email' => $entry->getAttribute('mail')[0] ?? null,
                    'title' => $entry->getAttribute('title')[0] ?? null,
                    'department' => $entry->getAttribute('department')[0] ?? null,
                    'isManager' => $this->isManager($entry->getAttribute('sAMAccountName')[0] ?? ''), // Appel à isManager
                ];
            }else{
                $admin[] = [$entry->getAttribute('sAMAccountName')[0] ?? null];
            }
        }
    
        return $users;
    }
    


    public function getVpnSslUsers(string $groupName, string $ouPath): array
    {
        $usersList = [];

        // Recherche du groupe dans l'OU
        $groupResults = $this->search(
            $ouPath,
            "(cn={$groupName})",
            ['distinguishedName', 'member']
        );

        if (empty($groupResults)) {
            return ['error' => "Le groupe '{$groupName}' n'a pas été trouvé dans l'OU spécifiée."];
        }

        $group = $groupResults[0];
        $members = $group->getAttribute('member') ?? [];

        // Récupérer les membres du groupe
        foreach ($members as $memberDn) {
            $userInfo = $this->search(
                $memberDn,
                '(objectClass=user)',
                ['displayName', 'sAMAccountName', 'manager']
            );

            if (!empty($userInfo)) {
                $user = $userInfo[0];
                $managerDn = $user->getAttribute('manager')[0] ?? null;

                // Récupérer les informations du gestionnaire
                $managerName = null;
                if ($managerDn) {
                    $managerResults = $this->search(
                        $managerDn,
                        '(objectClass=user)',
                        ['displayName']
                    );

                    $managerName = $managerResults[0]->getAttribute('displayName')[0] ?? 'Aucun gestionnaire trouvé';
                } else {
                    $managerName = 'Aucun gestionnaire trouvé';
                }

                // Ajouter les informations de l'utilisateur à la liste
                $usersList[] = [
                    'displayName' => $user->getAttribute('displayName')[0] ?? 'N/A',
                    'sAMAccountName' => $user->getAttribute('sAMAccountName')[0] ?? 'N/A',
                    'manager' => $managerName,
                ];
            }
        }

        return [
            'group' => $groupName,
            'members' => $usersList,
            'total' => count($usersList),
        ];
    }


    public function isUserInVpn(string $username, string $groupName, string $ouPath): bool
    {
        // Recherche du groupe spécifique dans l'OU
        $groupResults = $this->search(
            $ouPath,
            "(cn={$groupName})",
            ['member']
        );

        // Si le groupe n'est pas trouvé
        if (empty($groupResults)) {
            return false;
        }

        $group = $groupResults[0];
        $members = $group->getAttribute('member') ?? [];

        // Vérifier si l'utilisateur fait partie des membres
        foreach ($members as $memberDn) {
            $userInfo = $this->search(
                $memberDn,
                "(sAMAccountName={$username})",
                ['sAMAccountName']
            );

            if (!empty($userInfo)) {
                return true; // Utilisateur trouvé dans le groupe VPN
            }
        }

        return false; // Utilisateur non trouvé
    }


}
