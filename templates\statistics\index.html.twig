{% extends 'base.html.twig' %}

{% block title %}Statistiques{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        border: none;
        margin-bottom: 20px;
    }

    .card-header {
        border-radius: 8px 8px 0 0 !important;
        font-weight: 600;
    }

    .stats-card {
        transition: transform 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
    }

    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
    }

    .nav-tabs .nav-link.active {
        font-weight: 600;
        border-color: #dee2e6 #dee2e6 #fff;
    }

    .table th {
        background-color: #f1f5f9;
        font-weight: 600;
    }

    .progress {
        height: 20px;
        border-radius: 10px;
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-chart-line text-primary me-2"></i>
            Tableau de bord des statistiques
        </h1>
        <div>
            <a href="{{ path('app_statistics_workflow') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-project-diagram"></i> Analyse du workflow
            </a>
            <a href="{{ path('app_statistics_document_types') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-file-alt"></i> Types de documents
            </a>
            <a href="{{ path('app_time_tracking') }}" class="btn btn-secondary">
                <i class="fas fa-clock"></i> Suivi des temps
            </a>
        </div>
    </div>

    <!-- Vue d'ensemble -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ totalDocuments }}</div>
                    <div class="stats-label">Documents totaux</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ documentAgeDistribution.less_than_week }}</div>
                    <div class="stats-label">Documents < 1 semaine</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">{{ documentAgeDistribution.more_than_month }}</div>
                    <div class="stats-label">Documents > 1 mois</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-number">
                        {% if bottlenecks|length > 0 %}
                            {% set firstKey = bottlenecks|keys|first %}
                            {{ firstKey }}
                        {% else %}
                            N/A
                        {% endif %}
                    </div>
                    <div class="stats-label">État le plus lent</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et tableaux -->
    <div class="row">
        <!-- Répartition des documents par état -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Répartition des documents par état
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="stateDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Temps moyen par état -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-hourglass-half me-2"></i>
                        Temps moyen par état (jours)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="averageTimeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Goulots d'étranglement -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Goulots d'étranglement
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>État</th>
                                <th>Temps moyen (jours)</th>
                                <th>Impact</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for state, time in bottlenecks %}
                                <tr>
                                    <td>{{ state }}</td>
                                    <td>{{ time }}</td>
                                    <td>
                                        <div class="progress">
                                            {% set percentage = (time / bottlenecks|first) * 100 %}
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: {{ percentage }}%">
                                                {{ percentage|round }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Taux de retour -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-undo me-2"></i>
                        Taux de retour par état
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>État</th>
                                <th>Taux de retour</th>
                                <th>Pourcentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for state, rate in returnRates|slice(0, 5) %}
                                <tr>
                                    <td>{{ state }}</td>
                                    <td>{{ rate }}%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ rate }}%">
                                                {{ rate }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Répartition par âge -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Répartition des documents par âge
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="ageDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Données pour les graphiques
        const stateDistributionData = {
            labels: [{% for state, count in stateDistribution|slice(0, 10) %}'{{ state }}',{% endfor %}],
            datasets: [{
                label: 'Nombre de documents',
                data: [{% for state, count in stateDistribution|slice(0, 10) %}{{ count }},{% endfor %}],
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        const averageTimeData = {
            labels: [{% for state, time in averageTimeByState|slice(0, 10) %}'{{ state }}',{% endfor %}],
            datasets: [{
                label: 'Temps moyen (jours)',
                data: [{% for state, time in averageTimeByState|slice(0, 10) %}{{ time }},{% endfor %}],
                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        };

        const ageDistributionData = {
            labels: ['< 1 semaine', '1-2 semaines', '2-4 semaines', '> 1 mois'],
            datasets: [{
                label: 'Nombre de documents',
                data: [
                    {{ documentAgeDistribution.less_than_week }},
                    {{ documentAgeDistribution.one_to_two_weeks }},
                    {{ documentAgeDistribution.two_to_four_weeks }},
                    {{ documentAgeDistribution.more_than_month }}
                ],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Configuration des graphiques
        const barOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };

        const pieOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        };

        // Création des graphiques
        new Chart(
            document.getElementById('stateDistributionChart'),
            {
                type: 'bar',
                data: stateDistributionData,
                options: barOptions
            }
        );

        new Chart(
            document.getElementById('averageTimeChart'),
            {
                type: 'bar',
                data: averageTimeData,
                options: barOptions
            }
        );

        new Chart(
            document.getElementById('ageDistributionChart'),
            {
                type: 'pie',
                data: ageDistributionData,
                options: pieOptions
            }
        );
    });
</script>
{% endblock %}
