<div class="dropdown-menu p-3" style="width: 350px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
    <h6 class="dropdown-header text-center fw-bold mb-3" style="font-size: 1.1rem; color: #0d6efd;">
        <i class="fas fa-chart-line me-2"></i>Statistiques de temps
    </h6>

    <div class="row mb-3">
        <div class="col-6">
            <div class="card bg-light border-0 h-100">
                <div class="card-body p-3 text-center">
                    <h3 class="mb-0 text-primary">{{ totalDocuments }}</h3>
                    <small class="text-muted">Documents totaux</small>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="card bg-light border-0 h-100">
                <div class="card-body p-3 text-center">
                    <h3 class="mb-0 text-info">{{ documentsOutOfBE }}</h3>
                    <small class="text-muted">Documents hors BE</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-6">
            <div class="card bg-light border-0 h-100">
                <div class="card-body p-3 text-center">
                    <h3 class="mb-0 text-warning">{{ avgDaysSinceBE }}</h3>
                    <small class="text-muted">Jours moyens hors BE</small>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="card bg-light border-0 h-100">
                <div class="card-body p-3 text-center">
                    <h3 class="mb-0 text-danger">{{ maxDaysSinceBE }}</h3>
                    <small class="text-muted">Jours max hors BE</small>
                </div>
            </div>
        </div>
    </div>

    {% if documentWithMaxDays %}
    <div class="alert alert-warning mb-3 p-2 text-center" style="font-size: 0.9rem;">
        <strong>Document le plus ancien :</strong><br>
        <a href="{{ path('app_time_tracking_document', {'id': documentWithMaxDays.id}) }}" class="alert-link">
            {{ documentWithMaxDays.reference }} ({{ documentWithMaxDays.refRev }})
        </a>
    </div>
    {% endif %}

    <div class="dropdown-divider"></div>

    <div class="d-grid gap-2">
        <a href="{{ path('app_time_tracking') }}" class="btn btn-primary">
            <i class="fas fa-search me-2"></i> Voir tous les détails
        </a>
    </div>
</div>
