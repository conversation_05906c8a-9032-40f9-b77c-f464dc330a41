<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250312103245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE code (id INT AUTO_INCREMENT NOT NULL, phase_id INT NOT NULL, title VARCHAR(255) NOT NULL, code VARCHAR(255) NOT NULL, level INT DEFAULT NULL, status VARCHAR(255) DEFAULT NULL, work_center VARCHAR(255) DEFAULT NULL, INDEX IDX_7715309899091188 (phase_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE impute (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, code_id INT NOT NULL, nb_heures INT NOT NULL, INDEX IDX_B0156FCFA76ED395 (user_id), INDEX IDX_B0156FCF27DAFE17 (code_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE phase (id INT AUTO_INCREMENT NOT NULL, projet_id INT NOT NULL, title VARCHAR(255) NOT NULL, code VARCHAR(255) NOT NULL, level INT DEFAULT NULL, status TINYINT(1) NOT NULL, status_txt LONGTEXT DEFAULT NULL, status_manuel TINYINT(1) DEFAULT NULL, commentaire LONGTEXT DEFAULT NULL, INDEX IDX_B1BDD6CBC18272 (projet_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE code ADD CONSTRAINT FK_7715309899091188 FOREIGN KEY (phase_id) REFERENCES phase (id)');
        $this->addSql('ALTER TABLE impute ADD CONSTRAINT FK_B0156FCFA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE impute ADD CONSTRAINT FK_B0156FCF27DAFE17 FOREIGN KEY (code_id) REFERENCES code (id)');
        $this->addSql('ALTER TABLE phase ADD CONSTRAINT FK_B1BDD6CBC18272 FOREIGN KEY (projet_id) REFERENCES project (id)');
        $this->addSql('ALTER TABLE project DROP level, DROP description, DROP project_code, DROP status, DROP workcenter');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE code DROP FOREIGN KEY FK_7715309899091188');
        $this->addSql('ALTER TABLE impute DROP FOREIGN KEY FK_B0156FCFA76ED395');
        $this->addSql('ALTER TABLE impute DROP FOREIGN KEY FK_B0156FCF27DAFE17');
        $this->addSql('ALTER TABLE phase DROP FOREIGN KEY FK_B1BDD6CBC18272');
        $this->addSql('DROP TABLE code');
        $this->addSql('DROP TABLE impute');
        $this->addSql('DROP TABLE phase');
        $this->addSql('ALTER TABLE project ADD level INT DEFAULT NULL, ADD description LONGTEXT DEFAULT NULL, ADD project_code VARCHAR(255) DEFAULT NULL, ADD status VARCHAR(255) DEFAULT NULL, ADD workcenter VARCHAR(255) DEFAULT NULL');
    }
}
