<form method="post" action="{{ path('app_dmo_delete', {'id': dmo.id}) }}" class="delete_form d-inline">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ dmo.id) }}">
    <button type="submit" class="btn btn-link text-danger p-0 border-0 bg-transparent" title="Supprimer le DMO">
        <i class="me-2 fa fa-trash"></i> Supprimer
    </button>
</form>

<style>
    .my-icon0:hover {
        --primary: #ff0000;
        --secondary: #ff0000;
    }
</style>

<script>
    document.querySelectorAll('.my-icon0').forEach(icon => {
        icon.addEventListener('mouseover', () => {
            icon.setAttribute('colors', 'primary:#ff0000,secondary:#ff0000');
        });
        icon.addEventListener('mouseout', () => {
            icon.setAttribute('colors', 'primary:#000000,secondary:#000000');
        });
    });

    $('.delete_form').on('submit', function(e) {
        e.preventDefault();
        const form = this;
        Swal.fire({
            title: 'Supprimer ce DMO ?',
            text: 'Cette action est irréversible. Tous les commentaires associés seront également supprimés.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<i class="fas fa-trash-alt"></i> Oui, supprimer',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });
</script>
