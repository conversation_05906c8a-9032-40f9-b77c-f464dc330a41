<?php

namespace App\Repository;

use App\Entity\Impute;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Impute>
 */
class ImputeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Impute::class);
    }

    public function findByFilters($project, $user, $code, $phase, $yearMonth, $year, $workCenter, $ci, $sap): array
    {
        $qb = $this->createQueryBuilder('i');

        // On fait un seul JOIN pour 'code' si nécessaire
        $needJoinCode = $project || $code || $phase || $workCenter;
        if ($needJoinCode) {
            $qb->join('i.code', 'c');
        }
        
        // On fait un seul JOIN pour 'phase' si nécessaire
        if ($project || $phase) {
            $qb->join('c.phase', 'p');
        }
        
        // On fait un seul JOIN pour 'projet' si nécessaire
        if ($project) {
            $qb->join('p.projet', 'pr')
                ->andWhere('pr.id = :project')
                ->setParameter('project', $project);
        }
        
        // On fait un seul JOIN pour 'user' si nécessaire
        $needJoinUser = $user || $ci || $sap;
        if ($needJoinUser) {
            $qb->join('i.user', 'u');
        }
        
        if ($user) {
            $qb->andWhere('u.username LIKE :user')
                ->setParameter('user', '%' . $user . '%');
        }
        
        if ($code) {
            $qb->andWhere('c.code LIKE :code')
                ->setParameter('code', '%' . $code . '%');
        }
        
        if ($phase) {
            $qb->andWhere('p.title LIKE :phase OR p.code LIKE :phase')
                ->setParameter('phase', '%' . $phase . '%');
        }
        
        if ($yearMonth) {
            $qb->andWhere('i.createdAt LIKE :yearMonth')
                ->setParameter('yearMonth', '%' . $yearMonth . '%');
        }
        
        if ($year) {
            $qb->andWhere('i.createdAt LIKE :year')
                ->setParameter('year', $year . '%');
        }
        
        if ($workCenter) {
            $qb->andWhere('c.workCenter LIKE :workCenter')
                ->setParameter('workCenter', '%' . $workCenter . '%');
        }
        
        if ($ci) {
            if ($ci === 0) {
                $qb->andWhere('(u.ci = :ci OR u.ci IS NULL)')
                    ->setParameter('ci', $ci);
            } else {
                $qb->andWhere('u.ci = :ci')
                    ->setParameter('ci', $ci);
            }
        }
        
        if ($sap) {
            if ($sap === 0) {
                $qb->andWhere('(u.sap = :sap OR u.sap IS NULL)')
                    ->setParameter('sap', $sap);
            } else {
                $qb->andWhere('u.sap = :sap')
                    ->setParameter('sap', $sap);
            }
        }

        return $qb->getQuery()->getResult();
    }


    public function getTotalHeuresByCode(string $startDate, string $endDate, $projectId = null): array
    {
        $qb = $this->createQueryBuilder('i')
            ->join('i.code', 'c')
            ->select('c.title AS code_id, SUM(i.nbHeures) AS total_heures')
            ->where('i.createdAt BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate);
    
        if ($projectId !== null && $projectId !== 0) {
            $qb->join('c.phase', 'p')
               ->join('p.projet', 'pr')
               ->andWhere('pr.id = :projectId')
               ->setParameter('projectId', $projectId);
        }
    
        $qb->groupBy('c.title')
           ->orderBy('total_heures', 'DESC');
    
        return $qb->getQuery()->getResult();
    }
    
    public function getTotalHeuresByMonth(string $startDate, string $endDate, $projectId = null): array
    {
        $qb = $this->createQueryBuilder('i')
            ->select("DATE_FORMAT(i.createdAt, '%Y-%m') AS mois, SUM(i.nbHeures) AS total_heures")
            ->where('i.createdAt BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate);
    
        if ($projectId !== null && $projectId !== 0) {
            $qb->join('i.code', 'c')
               ->join('c.phase', 'p')
               ->join('p.projet', 'pr')
               ->andWhere('pr.id = :projectId')
               ->setParameter('projectId', $projectId);
        }
    
        $qb->groupBy("mois")
           ->orderBy("mois", "ASC");
    
        return $qb->getQuery()->getResult();
    }
    

    public function getTotalHeuresByMonthForUser(string $startDate, string $endDate, int $userId, $projectId = null): array
    {
        $qb = $this->createQueryBuilder('i')
            ->select("DATE_FORMAT(i.createdAt, '%Y-%m') AS mois, SUM(i.nbHeures) AS total_heures")
            ->where('i.createdAt BETWEEN :startDate AND :endDate')
            ->andWhere('i.user = :userId')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('userId', $userId);
    
        if ($projectId !== null && $projectId !== 0) {
            // On joint les entités nécessaires pour filtrer par projet
            $qb->join('i.code', 'c')
               ->join('c.phase', 'p')
               ->join('p.projet', 'pr')
               ->andWhere('pr.id = :projectId')
               ->setParameter('projectId', $projectId);
        }
    
        $qb->groupBy("mois")
           ->orderBy("mois", "ASC");
    
        return $qb->getQuery()->getResult();
    }
 
    

    public function findByFilters2(
        $project = null,
        $user = null,       // Peut être un identifiant numérique ou une chaîne pour recherche libre
        $code = null,
        $phase = null,
        $periode = null,
        $year = null,
        $workCenter = null,
        $ci = null,
        $sap = null,
        $managerFilter = null
    ): array {
        $qb = $this->createQueryBuilder('i')
            ->join('i.code', 'c')
            ->join('c.phase', 'p')
            ->join('p.projet', 'pr')
            ->join('i.user', 'u');
    
        if ($project) {
            $qb->andWhere('pr.id = :project')
               ->setParameter('project', $project);
        }
        // Si $user est numérique, filtrer par identifiant
        if ($user && is_numeric($user)) {
            $qb->andWhere('u.id = :userId')
               ->setParameter('userId', $user);
        } elseif ($user) {
            $qb->andWhere('u.username LIKE :user')
               ->setParameter('user', '%' . $user . '%');
        }
        if ($code) {
            $qb->andWhere('c.code LIKE :code')
               ->setParameter('code', '%' . $code . '%');
        }
        if ($phase) {
            $qb->andWhere('p.title LIKE :phase OR p.code LIKE :phase')
               ->setParameter('phase', '%' . $phase . '%');
        }
        if ($periode) {
            $qb->andWhere('i.createdAt LIKE :periode')
               ->setParameter('periode', $periode . '%');
        }
        if ($year) {
            $qb->andWhere('i.createdAt LIKE :year')
               ->setParameter('year', $year . '%');
        }
        if ($workCenter) {
            $qb->andWhere('c.workCenter LIKE :workCenter')
               ->setParameter('workCenter', '%' . $workCenter . '%');
        }
        if ($ci !== null && $ci !== '') {
            $qb->andWhere('u.ci = :ci')
               ->setParameter('ci', $ci);
        }
        if ($sap !== null && $sap !== '') {
            $qb->andWhere('u.sap = :sap')
               ->setParameter('sap', $sap);
        }
        if ($managerFilter) {
            $qb->andWhere('u.manager = :manager')
               ->setParameter('manager', $managerFilter);
        }
    
        return $qb->getQuery()->getResult();
    }

}
