{% extends 'base.html.twig' %}

{% block title %}Statistiques par type de document{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        border: none;
        margin-bottom: 20px;
    }
    
    .card-header {
        border-radius: 8px 8px 0 0 !important;
        font-weight: 600;
    }
    
    .stats-card {
        transition: transform 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
    }
    
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
    }
    
    .table th {
        background-color: #f1f5f9;
        font-weight: 600;
    }
    
    .progress {
        height: 20px;
        border-radius: 10px;
    }
    
    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
    }
    
    .nav-tabs .nav-link.active {
        font-weight: 600;
        border-color: #dee2e6 #dee2e6 #fff;
    }
    
    .doc-type-badge {
        font-size: 0.9rem;
        padding: 0.5em 0.8em;
        border-radius: 20px;
    }
    
    .doc-type-ASSY {
        background-color: #0d6efd;
    }
    
    .doc-type-MACH {
        background-color: #6f42c1;
    }
    
    .doc-type-MOLD {
        background-color: #fd7e14;
    }
    
    .doc-type-DOC {
        background-color: #20c997;
    }
    
    .doc-type-PUR {
        background-color: #dc3545;
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-file-alt text-primary me-2"></i>
            Statistiques par type de document
        </h1>
        <div>
            <a href="{{ path('app_statistics') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-chart-line"></i> Tableau de bord
            </a>
            <a href="{{ path('app_statistics_workflow') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-project-diagram"></i> Analyse du workflow
            </a>
            <a href="{{ path('app_time_tracking') }}" class="btn btn-secondary">
                <i class="fas fa-clock"></i> Suivi des temps
            </a>
        </div>
    </div>
    
    <!-- Vue d'ensemble des types de documents -->
    <div class="row mb-4">
        {% for docType, stats in docTypeStats %}
            <div class="col-md-2 mb-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center p-3">
                        <span class="badge doc-type-{{ docType }} text-white mb-2">{{ docType }}</span>
                        <div class="stats-number">{{ stats.count }}</div>
                        <div class="stats-label">Documents ({{ stats.percentage }}%)</div>
                        <div class="mt-2">
                            <span class="badge bg-info">{{ stats.avg_time }} jours en moyenne</span>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    
    <!-- Graphiques et tableaux -->
    <div class="row">
        <!-- Répartition par type de document -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Répartition par type de document
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="docTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Répartition par type de processus -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Répartition par type de processus
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="procTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <!-- Répartition par type de matériau -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cubes me-2"></i>
                        Répartition par type de matériau
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="materialTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Temps moyen par type de document -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-hourglass-half me-2"></i>
                        Temps moyen par type de document (jours)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="timeByDocTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Analyse détaillée par type de document -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search-plus me-2"></i>
                        Analyse détaillée par type de document
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="docTypeTabs" role="tablist">
                        {% for docType in docTypeStats|keys %}
                            <li class="nav-item" role="presentation">
                                <button class="nav-link {% if loop.first %}active{% endif %}" 
                                        id="tab-{{ docType }}" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#content-{{ docType }}" 
                                        type="button" 
                                        role="tab" 
                                        aria-controls="content-{{ docType }}" 
                                        aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                    <span class="badge doc-type-{{ docType }} text-white me-2">{{ docType }}</span>
                                    {{ docTypeStats[docType].count }} documents
                                </button>
                            </li>
                        {% endfor %}
                    </ul>
                    <div class="tab-content p-3" id="docTypeTabsContent">
                        {% for docType in docTypeStats|keys %}
                            <div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
                                 id="content-{{ docType }}" 
                                 role="tabpanel" 
                                 aria-labelledby="tab-{{ docType }}">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">États actuels des documents de type {{ docType }}</h6>
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>État</th>
                                                    <th>Nombre de documents</th>
                                                    <th>Pourcentage</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set totalDocs = docTypeStats[docType].count %}
                                                {% for state, count in statesByDocType[docType]|slice(0, 10) %}
                                                    <tr>
                                                        <td>{{ state }}</td>
                                                        <td>{{ count }}</td>
                                                        <td>
                                                            {% set percentage = (count / totalDocs) * 100 %}
                                                            <div class="progress">
                                                                <div class="progress-bar doc-type-{{ docType }}" role="progressbar" style="width: {{ percentage }}%">
                                                                    {{ percentage|round(1) }}%
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="mb-3">Temps moyen par état pour les documents de type {{ docType }}</h6>
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>État</th>
                                                    <th>Temps moyen (jours)</th>
                                                    <th>Impact</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set maxTime = 0 %}
                                                {% for state, data in timeByDocType[docType]|slice(0, 10) %}
                                                    {% if data.avg_days > maxTime %}
                                                        {% set maxTime = data.avg_days %}
                                                    {% endif %}
                                                {% endfor %}
                                                
                                                {% for state, data in timeByDocType[docType]|slice(0, 10) %}
                                                    <tr>
                                                        <td>{{ state }}</td>
                                                        <td>{{ data.avg_days }}</td>
                                                        <td>
                                                            {% set percentage = maxTime > 0 ? (data.avg_days / maxTime) * 100 : 0 %}
                                                            <div class="progress">
                                                                <div class="progress-bar doc-type-{{ docType }}" role="progressbar" style="width: {{ percentage }}%">
                                                                    {{ percentage|round(1) }}%
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Données pour les graphiques
        const docTypeData = {
            labels: [{% for docType, stats in docTypeStats %}'{{ docType }}',{% endfor %}],
            datasets: [{
                label: 'Nombre de documents',
                data: [{% for docType, stats in docTypeStats %}{{ stats.count }},{% endfor %}],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(111, 66, 193, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(32, 201, 151, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(111, 66, 193, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(32, 201, 151, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        const procTypeData = {
            labels: [{% for procType, stats in procTypeStats %}'{{ procType }}',{% endfor %}],
            datasets: [{
                label: 'Nombre de documents',
                data: [{% for procType, stats in procTypeStats %}{{ stats.count }},{% endfor %}],
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
        
        const materialTypeData = {
            labels: [{% for materialType, stats in materialTypeStats %}'{{ materialType }}',{% endfor %}],
            datasets: [{
                label: 'Nombre de documents',
                data: [{% for materialType, stats in materialTypeStats %}{{ stats.count }},{% endfor %}],
                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        };
        
        const timeByDocTypeData = {
            labels: [{% for docType, stats in docTypeStats %}'{{ docType }}',{% endfor %}],
            datasets: [{
                label: 'Temps moyen (jours)',
                data: [{% for docType, stats in docTypeStats %}{{ stats.avg_time }},{% endfor %}],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(111, 66, 193, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(32, 201, 151, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(111, 66, 193, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(32, 201, 151, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        // Configuration des graphiques
        const pieOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        };
        
        const barOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };
        
        // Création des graphiques
        new Chart(
            document.getElementById('docTypeChart'),
            {
                type: 'pie',
                data: docTypeData,
                options: pieOptions
            }
        );
        
        new Chart(
            document.getElementById('procTypeChart'),
            {
                type: 'bar',
                data: procTypeData,
                options: barOptions
            }
        );
        
        new Chart(
            document.getElementById('materialTypeChart'),
            {
                type: 'bar',
                data: materialTypeData,
                options: barOptions
            }
        );
        
        new Chart(
            document.getElementById('timeByDocTypeChart'),
            {
                type: 'bar',
                data: timeByDocTypeData,
                options: barOptions
            }
        );
    });
</script>
{% endblock %}
