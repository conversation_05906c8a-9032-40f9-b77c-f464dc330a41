{% extends 'base.html.twig' %}

{% block title %}Document Index{% endblock %}

{% block body %}
<div class="container mt-5">
    <h1 class="text-center mb-4">Document List</h1>
    
    <div class="card shadow-sm">
        <div class="card-body">
            <table class="table table-hover table-striped table-bordered">
                <thead class="thead-dark">
                    <tr class="text-center">
                        <th>Id</th>
                        <th>Reference</th>
                        <th>RefRev</th>
                        <th>RefTitleFra</th>
                        <th>ProdDraw</th>
                        <th>ProdDrawRev</th>
                        <th>DrawingPath</th>
                        <th>Alias</th>
                        <th>DocType</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                {% for document in documents %}
                    <tr>
                        <td class="text-center align-middle">{{ document.id }}</td>
                        <td class="text-center align-middle">{{ document.reference }}</td>
                        <td class="text-center align-middle">{{ document.refRev }}</td>
                        <td class="text-center align-middle">{{ document.refTitleFra }}</td>
                        <td class="text-center align-middle">{{ document.prodDraw }}</td>
                        <td class="text-center align-middle">{{ document.prodDrawRev }}</td>
                        <td class="text-center align-middle"></td>
                        <td class="text-center align-middle">{{ document.alias }}</td>
                        <td class="text-center align-middle">{{ document.docType }}</td>
                        <td class="text-center align-middle">
                            <div class="d-flex justify-content-center align-items-center">
                                {# <a href="{{ path('app_document_edit', {'id': document.id}) }}" class="btn btn-sm mx-1">
                                    <lord-icon
                                        src="https://cdn.lordicon.com/exymduqj.json"
                                        trigger="hover"
                                        state="hover-line"
                                        colors="primary:#121331,secondary:#109121"
                                        style="width:25px;height:25px">
                                    </lord-icon>
                                </a> #}
                                {% include 'document/_delete_form.html.twig' %}
                            </div>
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="10" class="text-center">No records found</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ path('app_document_new') }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Create New Document        </a>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
    }
    .table {
        margin-bottom: 0;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}
