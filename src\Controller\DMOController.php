<?php

namespace App\Controller;

use App\Entity\DMO;
use App\Form\DMO1Type;
use App\Repository\DMORepository;
use App\Repository\ProductRangeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use App\Repository\UserRepository;
use App\Entity\User;
use App\Entity\Project;
use App\Repository\ProjectRepository;
use App\Entity\Commentaire;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Knp\Component\Pager\PaginatorInterface;

#[Route('/dmo')]
final class DMOController extends AbstractController
{
    #[Route(name: 'app_dmo_index', methods: ['GET'])]
    public function index(Request $request, UserRepository $userRepository, DMORepository $dMORepository, ProjectRepository $projectRepository, PaginatorInterface $paginator): Response
    {
        // Récupération de tous les paramètres de recherche
        $criteria = $request->query->all();

        // Récupérer le QueryBuilder avec les critères de recherche
        $queryBuilder = $dMORepository->findBySearchCriteria($criteria);

        // Pagination
        $pagination = $paginator->paginate(
            $queryBuilder,
            $request->query->getInt('page', 1),
            20 // 20 DMO par page
        );

        $projects = $projectRepository->findAll();
        return $this->render('dmo/index.html.twig', [
            'users' => $userRepository->findAll(),
            'users_bde' => $userRepository->findBy(['departement' =>  "Bureau d'Etudes"]),
            'dmos' => $pagination,
            'projects' => $projects,
        ]);
    }

    #[Route('/new', name: 'app_dmo_new', methods: ['GET'])]
    public function render_create(): Response
    {
        return $this->render('dmo/create_form.html.twig');
    }

    #[Route('/test/{id}', name: 'app_dmo_test', methods: ['GET'])]
    public function test(DMO $dmo): Response
    {
        return $this->render('dmo/brouillon.html.twig', [
            'dmo' => $dmo,
        ]);
    }

    #[Route('/export-csv-template', name: 'app_dmo_export_csv_template', methods: ['GET'])]
    public function exportCsvTemplate(DMORepository $dmoRepository): Response
    {
        $dmos = $dmoRepository->findAll();
        return $this->render('dmo/export_csv.html.twig', [
            'dmos' => $dmos
        ]);
    }

    #[Route('/export-csv', name: 'app_dmo_export_csv', methods: ['GET'])]
    public function exportCsv(Request $request, DMORepository $dmoRepository): Response
    {
        $allQuery = $request->query->all();
        $selectedColumns = isset($allQuery['columns']) ? $allQuery['columns'] : [];

        $availableColumns = [
            'dmo' => 'getDmoId',
            'date_init' => function(DMO $dmo) {
                $d = $dmo->getDateInit();
                return $d instanceof \DateTimeInterface ? $d->format('Y-m-d') : null;
            },
            'Description' => 'getDescription',
            'Project' => function(DMO $dmo) {
                $project = $dmo->getProjectRelation();
                return $project ? $project->getOTP() : '';
            },
            'requestor' => function(DMO $dmo) { return $dmo->getRequestor() ? $dmo->getRequestor()->getNom().' '.$dmo->getRequestor()->getPrenom() : ''; },
            'Decision' => 'getDecision',
            'status' => function(DMO $dmo) { return $dmo->isStatus() ? 'Open' : 'Closed'; },
            'Ex' => 'getEx',
            'Indus_Related' => function(DMO $dmo) { return $dmo->isIndusRelated() ? 'Yes' : 'No'; },
            'Eng_Owner' => function(DMO $dmo) {
                $engOwner = $dmo->getEngOwner();
                return $engOwner ? $engOwner->getNom().' '.$engOwner->getPrenom() : '';
            },
            'date_end' => 'getDateEnd',
            'Pr_Number' => 'getPrNumber',
            'last_Modificator' => function(DMO $dmo) {
                $lastModificator = $dmo->getLastModificator();
                return $lastModificator ? $lastModificator->getNom().' '.$lastModificator->getPrenom() : '';
            },
            'last_Update_Date' => 'getLastUpdateDate',
            'Ex_Assessment' => 'getExAssessment',
            'Spent_Time' => 'getSpentTime',
            'Type' => function(DMO $dmo) { return $dmo->getType() ?? ""; },
            'Document' => 'getDocument',
            'productRange' => function(DMO $dmo) {
                return $dmo->getNameDivisonProductRange() . ' / ' . $dmo->getNameProductRange();
            },
        ];

        if (empty($selectedColumns)) {
            $selectedColumns = array_keys($availableColumns);
        }

        // Utiliser une requête optimisée pour l'export
        $dmos = $dmoRepository->createQueryBuilder('d')
            ->orderBy('d.date_init', 'DESC')
            ->getQuery()
            ->getResult();

        $response = new StreamedResponse();
        $response->setCallback(function() use ($dmos, $selectedColumns, $availableColumns) {
            $handle = fopen('php://output', 'w+');

            $header = [];
            foreach ($selectedColumns as $col) {
                if (isset($availableColumns[$col])) {
                    $header[] = strtoupper($col);
                }
            }
            fputcsv($handle, $header, ';');

            foreach ($dmos as $dmo) {
                $row = [];
                foreach ($selectedColumns as $col) {
                    if (isset($availableColumns[$col])) {
                        $getter = $availableColumns[$col];
                        $value = null;
                        if (is_callable($getter)) {
                            $value = $getter($dmo);
                        } elseif (method_exists($dmo, $getter)) {
                            $value = $dmo->$getter();
                            if ($value instanceof \DateTimeInterface) {
                                $value = $value->format('Y-m-d');
                            }
                            elseif (is_object($value) && method_exists($value, 'getUsername')) {
                                $value = $value->getUsername();
                            }
                        }
                        $row[] = $value;
                    }
                }
                fputcsv($handle, $row, ';');
            }
            fclose($handle);
        });

        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="dmos_export.csv"');
        return $response;
    }


    #[Route('/addCommentdmo', name: 'app_dmo_add_comment', methods: ['POST'])]
    public function addCommentDmo(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $comment = $request->request->get('comment');
        $dmoId = $request->request->get('dmoId');
        $user = $this->getUser();
        $departement = $user->getDepartement();
        $dmo = $entityManager->getRepository(DMO::class)->find($dmoId);

        if (!$dmo) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'DMO non trouvé.'
            ], Response::HTTP_NOT_FOUND);
        }

        $commentaire = new Commentaire();
        $commentaire->setCommentaire($comment);
        $commentaire->setCreatedAt(new \DateTimeImmutable());
        $commentaire->setUser($user);
        $commentaire->setDmoId($dmo);
        $commentaire->setType('DMO');
        $commentaire->setState($departement);

        $entityManager->persist($commentaire);
        $entityManager->flush();

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Commentaire ajouté avec succès.'
        ]);
    }


    #[Route('/create', name: 'app_dmo_create', methods: ['GET', 'POST'])]
    public function create(Request $request, EntityManagerInterface $entityManager, ProductRangeRepository $productRangeRepository, ProjectRepository $projectRepository): Response
    {
        $dmo = new DMO();
        $user = $this->getUser();

        if ($request->isMethod('POST')) {
            $type = $request->request->get('type');
            $document = $request->request->get('document');
            $division = $request->request->get('division');
            $productRangeName = $request->request->get('productRange');
            $project = $request->request->get('project');
            $description = $request->request->get('description');

            $productRange = null;
            if (!empty($productRangeName) && !empty($division)) {
                $productRange = $productRangeRepository->findOneBy(['ProductRange' => $productRangeName, 'Division' => $division]);
            }

            // Si ProductRange n'est pas trouvé, utiliser "Unknown" pour la division
            if (!$productRange && !empty($division)) {
                $productRange = $productRangeRepository->findOneBy(['ProductRange' => 'Unknown', 'Division' => $division]);

                // Si "Unknown" n'existe pas pour cette division, le créer
                if (!$productRange) {
                    $productRange = $this->createUnknownProductRange($division, $entityManager, $productRangeRepository);
                }
            }

            $projectR = $projectRepository->find($project);

            // Créer le DMO (ProductRange peut être null maintenant)
            $dmo->setProductRange($productRange);
            $dmo->setType($type);
            $dmo->setDocument($document);
            $dmo->setProjectRelation($projectR);
            $dmo->setDescription($description);
            $dmo->setDateInit(new \DateTimeImmutable());
            $dmo->setStatus(true);
            $dmo->setDecision('CREATED');
            $dmo->setLastUpdateDate(new \DateTimeImmutable());
            $dmo->setSpentTime(0);
            $dmo->setRequestor($user);

            $entityManager->persist($dmo);
            $entityManager->flush();

            // Générer l'identifiant DMO après la persistance (quand l'ID est disponible)
            $this->generateDmoIdentifier($dmo, $entityManager);

            $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/DMO' . $dmo->getId();

                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0775, true);
                }

                $files = $request->files->get('mesfichiers');
                if ($files) {
                    foreach ($files as $file) {
                        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                        $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
                        $newFilename = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();

                        try {
                            $file->move($uploadDir, $newFilename);
                        } catch (FileException $e) {
                            $this->addFlash('error', 'Erreur lors de l\'upload du fichier.');
                        }
                    }
                }

            $this->addFlash('success', 'DMO créé avec succès');
            return $this->redirectToRoute('app_dmo_index');
        }

        return $this->render('dmo/create_dmo.html.twig');
    }

    /**
     * Créer un ProductRange "Unknown" pour une division
     */
    private function createUnknownProductRange(string $division, EntityManagerInterface $entityManager, ProductRangeRepository $productRangeRepository): ?ProductRange
    {
        try {
            $unknownProductRange = new \App\Entity\ProductRange();
            $unknownProductRange->setProductRange('Unknown');
            $unknownProductRange->setDivision($division);
            $unknownProductRange->setEtat(false); // Inactif par défaut

            $entityManager->persist($unknownProductRange);
            $entityManager->flush();

            return $unknownProductRange;
        } catch (\Exception $e) {
            return null;
        }
    }



    #[Route('/getProductRange', name: 'app_dmo_getProductRange', methods: ['GET'])]
    public function getProductRange(ProductRangeRepository $productRangeRepository): Response
    {
        $productRanges = $productRangeRepository->findBy([], ['Division' => 'ASC', 'ProductRange' => 'ASC']);
        $groupedProductRanges = [];
        foreach ($productRanges as $productRange) {
            $division = $productRange->getDivision();
            if (!isset($groupedProductRanges[$division])) {
                $groupedProductRanges[$division] = [];
            }
            $groupedProductRanges[$division][] = $productRange->getProductRange();
        }

        return $this->json($groupedProductRanges);
    }

    #[Route('/getAllProjectRelation', name: 'app_dmo_getAllProjectRelation', methods: ['GET'])]
    public function getAllProjectRelation(Request $request, ProjectRepository $projectRepository): Response
    {
        $projects = $projectRepository->findAll();
        return $this->json($projects, 200, [], [
            'attributes' => ['id', 'OTP']
        ]);
    }


    #[Route('/show/{id}', name: 'app_dmo_show', methods: ['GET'])]
    public function show(DMO $dMO, UserRepository $userRepository, ProductRangeRepository $productRangeRepository,ProjectRepository $projectRepository): Response
    {
        $division = $dMO->getProductRange()?->getDivision();
        $productRanges = [];
        if ($division) {
            $productRanges = $productRangeRepository->findBy(['Division' => $division]);
        }
        $projects = $projectRepository->findAll();
        return $this->render('dmo/show.html.twig', [
            'dmo' => $dMO,
            'users_bde' => $userRepository->findBy(['departement' =>  "Bureau d'Etudes"]),
            'productRanges' => $productRanges,
            'projects' => $projects,
        ]);
    }


    #[Route('/{id}/update', name: 'app_dmo_update', methods: ['POST'])]
    public function update(
        DMO $dmo,
        Request $request,
        EntityManagerInterface $entityManager,
        UserRepository $userRepository,
        ProjectRepository $projectRepository,
        ProductRangeRepository $productRangeRepository
    ): JsonResponse {

        $user = $this->getUser();
        $engOwnerId     = $request->request->get('EngOwner');
        $exAssessment   = $request->request->get('ExAssessment');
        $spentTime      = $request->request->get('SpentTime');
        $ex             = $request->request->get('Ex');
        $productRangeId = $request->request->get('productRange');
        $prNumber       = $request->request->get('PrNumber');
        $project        = $request->request->get('Project');
        $document       = $request->request->get('Document');
        $type           = $request->request->get('Type');

        $projectR = $projectRepository->find($project);
        $dmo->setLastModificator($user);
        if ($engOwnerId !== null && trim($engOwnerId) !== '') {
            $engOwner = $userRepository->find($engOwnerId);
            if ($engOwner) {
                $dmo->setEngOwner($engOwner);
            }
        }

        if ($exAssessment !== null) {
            $trimmedExAssessment = trim($exAssessment);
            $dmo->setExAssessment($trimmedExAssessment !== '' ? $trimmedExAssessment : null);
        }

        if ($spentTime !== null && trim($spentTime) !== '') {
            if (is_numeric($spentTime)) {
                $dmo->setSpentTime((int)$spentTime);
            } else {
                return new JsonResponse([
                    'status'  => 'error',
                    'message' => 'SpentTime doit être numérique.'
                ], Response::HTTP_BAD_REQUEST);
            }
        } else {
            $dmo->setSpentTime(0);
        }

        if ($ex !== null) {
            $trimmedEx = trim($ex);
            $dmo->setEx($trimmedEx !== '' ? $trimmedEx : null);
        }

        if ($productRangeId !== null && trim($productRangeId) !== '') {
            $productRange = $productRangeRepository->find($productRangeId);
            if ($productRange) {
                $dmo->setProductRange($productRange);
            }
        }

        if ($prNumber !== null && trim($prNumber) !== '') {
            if (is_numeric($prNumber)) {
                $dmo->setPrNumber((int)$prNumber);
            } else {
                return new JsonResponse([
                    'status'  => 'error',
                    'message' => 'PrNumber doit être numérique.'
                ], Response::HTTP_BAD_REQUEST);
            }
        } else {
            $dmo->setPrNumber(null);
        }

        if ($project !== null) {
            // $trimmedProject = trim($project);
            // $dmo->setProject($trimmedProject !== '' ? $trimmedProject : null);
            $dmo->setProjectRelation($projectR);
        }

        if ($document !== null) {
            $trimmedDocument = trim($document);
            $dmo->setDocument($trimmedDocument !== '' ? $trimmedDocument : null);
        }

        if ($type !== null && trim($type) !== '') {
            $dmo->setType(trim($type));
        } else {
            return new JsonResponse([
                'status'  => 'error',
                'message' => 'Le champ Type est obligatoire.'
            ], Response::HTTP_BAD_REQUEST);
        }

        $action = $request->request->get('action');
        switch ($action) {
            case 'accept':
                $dmo->setDecision('ACCEPTED');
                $dmo->setStatus(false);
                break;
            case 'reject':
                $dmo->setDecision('REJECTED');
                $dmo->setStatus(false);
                break;
            case 'under-review':
                $dmo->setDecision('UNDER REVIEW');
                break;
            case 'update':
            default:
                break;
        }

        $dmo->setLastUpdateDate(new \DateTimeImmutable());

        $entityManager->flush();

        return new JsonResponse([
            'status'  => 'success',
            'message' => 'DMO mis à jour avec succès.'
        ]);
    }



    #[Route('/{id}', name: 'app_dmo_delete', methods: ['POST'])]
    public function delete(Request $request, DMO $dMO, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$dMO->getId(), $request->getPayload()->getString('_token'))) {
            try {
                // Supprimer d'abord les commentaires liés (contrainte FK)
                $commentaires = $dMO->getCommentaires();
                foreach ($commentaires as $commentaire) {
                    $entityManager->remove($commentaire);
                }

                // Supprimer les relations avec les ReleasedPackage
                foreach ($dMO->getReleasedPackages() as $package) {
                    $package->removeDmo($dMO);
                }

                // Maintenant supprimer le DMO
                $entityManager->remove($dMO);
                $entityManager->flush();

                $this->addFlash('success', 'DMO supprimé avec succès.');

            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du DMO : ' . $e->getMessage());
            }
        } else {
            $this->addFlash('error', 'Token CSRF invalide.');
        }

        return $this->redirectToRoute('app_dmo_index', [], Response::HTTP_SEE_OTHER);
    }

    /**
     * Génère automatiquement l'identifiant DMO basé sur la date (DMO + AAMMDD + caractère)
     */
    private function generateDmoIdentifier(DMO $dmo, EntityManagerInterface $entityManager): void
    {
        $dateInit = $dmo->getDateInit();
        $dateFormat = $dateInit->format('ymd'); // AAMMDD (24 pour 2024, 12 pour décembre, 05 pour le 5)

        // Chercher tous les DMO créés le même jour
        $dayPattern = 'DMO' . $dateFormat;

        $existingDmosToday = $entityManager->getRepository(DMO::class)
            ->createQueryBuilder('d')
            ->where('d.dmo LIKE :dayPattern')
            ->setParameter('dayPattern', $dayPattern . '%')
            ->orderBy('d.dmo', 'DESC')
            ->getQuery()
            ->getResult();

        // Déterminer le prochain caractère (A, B, C, etc.)
        if (empty($existingDmosToday)) {
            // Premier DMO du jour
            $nextChar = 'A';
        } else {
            // Trouver le dernier caractère utilisé
            $lastDmo = $existingDmosToday[0];
            $lastDmoId = $lastDmo->getDmo();

            if (strlen($lastDmoId) >= 10) { // DMO + 6 chiffres + 1 caractère minimum
                $lastChar = substr($lastDmoId, -1); // Dernier caractère
                $nextChar = chr(ord($lastChar) + 1); // Caractère suivant (A->B, B->C, etc.)
            } else {
                $nextChar = 'A';
            }
        }

        // Générer l'identifiant DMO (ex: DMO241205A, DMO241205B, etc.)
        $dmoIdentifier = $dayPattern . $nextChar;

        $dmo->setDmo($dmoIdentifier);
        $entityManager->flush();
    }

    #[Route('/dmo/upload', name: 'dmo_upload', methods: ['POST'])]
    public function upload(Request $request): JsonResponse
    {
        $file = $request->files->get('file');
        $id = $request->request->get('id');

        if (!$file || !$id) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Fichier ou ID manquant.'
            ], Response::HTTP_BAD_REQUEST);
        }

        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/DMO' . $id;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0775, true);
        }

        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = preg_replace('/[^a-z0-9]+/', '-', strtolower($originalFilename));
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $file->guessExtension();

        try {
            $file->move($uploadDir, $newFilename);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Erreur lors de l\'upload du fichier.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Fichier uploadé avec succès.'
        ]);
    }

    #[Route('/dmo/getFiles', name: 'dmo_get_files', methods: ['GET'])]
    public function getFiles(Request $request): JsonResponse
    {
        $id = $request->query->get('id');

        if (!$id) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'ID manquant.'
            ], Response::HTTP_BAD_REQUEST);
        }

        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/DMO' . $id;

        if (!is_dir($uploadDir)) {
            return new JsonResponse([
                'status' => 'success',
                'files' => []
            ]);
        }

        $files = [];
        foreach (scandir($uploadDir) as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            $files[] = '/uploads/DMO' . $id . '/' . $file;
        }

        return new JsonResponse([
            'status' => 'success',
            'files' => $files
        ]);
    }


    #[Route('/dmo/deleteFile', name: 'dmo_delete_file', methods: ['POST'])]
    public function deleteFile(Request $request): JsonResponse
    {
        $fileUrl = $request->request->get('file');

        if (!$fileUrl) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Fichier non spécifié.'
            ], Response::HTTP_BAD_REQUEST);
        }

        $filePath = $this->getParameter('kernel.project_dir') . '/public' . $fileUrl;

        if (!file_exists($filePath)) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Fichier non trouvé.'
            ], Response::HTTP_NOT_FOUND);
        }
        try {
            unlink($filePath);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Erreur lors de la suppression du fichier.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Fichier supprimé avec succès.'
        ]);
    }

    #[Route('/pdf/{filename}', name: 'app_dmo_pdf', methods: ['GET'])]
    public function pdf($filename): Response
    {
        $path = $this->getParameter('kernel.project_dir') . '/public/pdf/' . $filename;
        return new Response(file_get_contents($path), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $filename . '"'
        ]);
    }
}
