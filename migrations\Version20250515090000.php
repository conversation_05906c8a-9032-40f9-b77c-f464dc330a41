<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour ajouter le champ managedPlaces à la table user
 */
final class Version20250515090000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute le champ managedPlaces à la table user';
    }

    public function up(Schema $schema): void
    {
        // Ajouter le champ managedPlaces à la table user
        $this->addSql('ALTER TABLE user ADD managed_places JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Supprimer le champ managedPlaces de la table user
        $this->addSql('ALTER TABLE user DROP managed_places');
    }
}
